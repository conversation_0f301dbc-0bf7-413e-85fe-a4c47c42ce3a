//package os.tukan.extractor.vectorDB;
//
//
//import com.azure.cosmos.CosmosClient;
//import com.azure.cosmos.CosmosClientBuilder;
//import com.azure.cosmos.CosmosContainer;
//import com.azure.cosmos.CosmosDatabase;
//import com.azure.identity.DefaultAzureCredential;
//import com.azure.identity.DefaultAzureCredentialBuilder;
//import lombok.extern.log4j.Log4j2;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import jakarta.json.Json;
//import jakarta.json.JsonArray;
//import jakarta.json.JsonObject;
//
//import java.io.IOException;
//import java.io.InputStream;
//import java.net.URI;
//import java.net.http.HttpClient;
//import java.net.http.HttpRequest;
//import java.net.http.HttpResponse;
//
//@Log4j2
//@Service
//public class CosmoDbService {
//    private final CosmosClient cosmosClient;
//    private CosmosContainer cosmosContainer;
//    private final HttpClient httpClient;
//
//    private final ObjectMapper objectMapper;
//    private final String host = "http://*************:11434/api/embed";
//    private final String model = "jina/jina-embeddings-v2-base-de";
//
//    private final String databaseName = "index";
//
//    public CosmoDbService()  {
//
//        String endpoint = "http://*************:8081";
//        DefaultAzureCredential credential = new DefaultAzureCredentialBuilder().build();
//
//        String key = "C2y6yDjf5/R+ob0N8A7Cgv30VRDJIWEHLM+4QDU5DE2nQ9nDuVTqobD4b8mGGyPMbIZnqyMsEcaGQy67XIw/Jw==";
//        this.cosmosClient = new CosmosClientBuilder()
//                .endpoint(endpoint)
//                .key(key)
//                .gatewayMode()
//                .buildClient();
//
//
//        this.httpClient = HttpClient.newBuilder().build();
//        this.objectMapper = new ObjectMapper();
//
//        CosmosDatabase database = cosmosClient.getDatabase(databaseName);
//
//        if (database.getId().isEmpty()) {
//            this.createDatabaseIfNotExists(databaseName);
//        }
//
//        // Create or get the database
//
////        this.createVectorContainerIfNotExists();
////        cosmosClient.createDatabaseIfNotExists(databaseName);
////        CosmosDatabase database = cosmosClient.getDatabase(databaseName);
////        this.cosmosContainer = cosmosClient.getDatabase(this.databaseName).getContainer(this.containerName);
//
//
//
//
//    }
//
//    /**
//     * Create the database if it does not exist.
//     */
//    private void createDatabaseIfNotExists(String dbName) {
//        cosmosClient.createDatabaseIfNotExists(databaseName);
//        log.info("Database '{}' is ready.", dbName);
//    }
//
//    /**
//     * Create a container with a vector index (Preview).
//     * Adjust the indexing policy to enable vector indexing on "embedding".
////     */
////    private void createVectorContainerIfNotExists() {
////        CosmosDatabase database = cosmosClient.getDatabase(this.databaseName);
////
////        // In preview, you must specify a special index policy for your "embedding" field,
////        // e.g., HNSW configuration if using "cosine" or "euclidean" distance. The example below is conceptual:
////        //
////        // If your Cosmos DB has an ARM property for vector indexing, you might be able
////        // to define the index like:
////        //
////        //   "indexes": [
////        //       {
////        //           "kind": "vector",
////        //           "path": "/embedding",
////        //           "config": {
////        //               "dimensions": 1536,
////        //               "metric": "Cosine",
////        //               "capacity": 10000
////        //           }
////        //       }
////        //   ]
////        //
////        // But many times you’ll define the vector index at container creation using Azure CLI, or Portal, not purely in code.
////        // Below is a simple example showing how to specify indexing policy programmatically in Java.
////        // (This may change as the preview evolves.)
////
////        IndexingPolicy indexingPolicy = new IndexingPolicy()
////                .setAutomatic(true)
////                .setIndexingMode(IndexingMode.CONSISTENT);
////
////        // If your library version supports specifying a vector index in code, you would do something like:
////        // indexingPolicy.setIndexes(List.of(
////        //     new VectorIndexPolicy(
////        //         "embedding",
////        //         new HnswConfig(/* dims= */1536, /* metric= */"cosine", /*some other config...*/),
////        //         true // or false for `excluded`?
////        //     )
////        // ));
////
////        // Create container properties with the indexing policy
////        CosmosContainerProperties containerProperties = new CosmosContainerProperties("container", "id")
////                .setIndexingPolicy(indexingPolicy);
////
////        database.createContainerIfNotExists(containerProperties, ThroughputProperties.createManualThroughput(400));
////        this.cosmosContainer = database.getContainer("container");
////
////        log.info("Container '{}' with vector index is ready.", "container");
////    }
//
////    public void upsertDocumentWithEmbedding(String docId, String text, float[] embedding) {
////        // Create a simple JSON-like map representing your document
////        // You can have more fields if needed (metadata, etc.).
////        Map<String, Object> docMap = Map.of(
////                "id", docId,
////                "text", text,
////                VECTOR_FIELD, embedding // embedding is stored as a float array
////        );
////
////        // Upsert the document into Cosmos DB
////        // Partition key is "id" in this example
////        CosmosItemResponse<?> response = cosmosContainer.upsertItem(docMap);
////        log.info("Upserted document. Status code: {}", response.getStatusCode());
////    }
//
//    /**
//     * Example method: store raw text by generating the embedding, then upserting.
//     */
//    public void upsertDocument(String text) throws Exception {
//        // Generate a new ID
//        String docId = UUID.randomUUID().toString();
//        // 1) Generate embedding using your method
//        float[] embedding = generateEmbedding(text);
//        // 2) Upsert
////        upsertDocumentWithEmbedding(docId, text, embedding);
//    }
//
//    /**
//     * Example method: vector similarity search.
//     * <p>
//     * Cosmos DB provides a special SQL function: `COSMOSDB_APPROXIMATE_DISTANCE`
//     * or `COSMOSDB_VECTOR_DISTANCE` in preview. We'll assume approximate distance.
//     * You can order by that function or filter by a threshold.
//     * <p>
//     * This query orders results by similarity to the given searchEmbedding
//     * and returns the top N results.
//     */
////    public List<Map<String, Object>> vectorSearch(float[] searchEmbedding, int topN) {
////        // We can pass the embedding as a parameter to the query
////        // and order by COSMOSDB_APPROXIMATE_DISTANCE(c.embedding, @embedding).
////        // (Note: The exact function name may differ based on your preview)
////        // Example:
////        // "SELECT TOP @topN * FROM c ORDER BY COSMOSDB_APPROXIMATE_DISTANCE(c.embedding, @embedding)"
////
////        String queryText = String.format(
////                "SELECT TOP %d * FROM c ORDER BY COSMOSDB_APPROXIMATE_DISTANCE(c.%s, @embedding)",
////                topN, VECTOR_FIELD
////        );
////
////        SqlQuerySpec querySpec = new SqlQuerySpec(
////                queryText,
////                (List<SqlParameter>) Map.of("@embedding", searchEmbedding)
////        );
////
////        CosmosPagedIterable<Map<String, Object>> results = cosmosContainer.queryItems(
////                querySpec,
////                new CosmosQueryRequestOptions(),
////                (Class<Map<String, Object>>) (Class<?>) Map.class
////        );
////
////        // Gather into a list
////        List<Map<String, Object>> resultList = new ArrayList<>();
//////        for (Map<String, Object> item : results) {
//////            resultList.add(item);
//////        }
////
////        return resultList;
////    }
//
//    /**
//     * Your existing embedding generation method. Provided for reference
//     * but you indicated you have something like this already.
//     */
//    public float[] generateEmbedding(String text) throws IOException, InterruptedException {
//        Map<String, Object> requestBody = Map.of(
//                "model", model,
//                "input", text
//        );
//        long start = System.currentTimeMillis();
//
//        String jsonRequestBody = objectMapper.writeValueAsString(requestBody);
//        HttpRequest request = HttpRequest.newBuilder()
//                .uri(URI.create(host))
//                .header("Content-Type", "application/json")
//                .POST(HttpRequest.BodyPublishers.ofString(jsonRequestBody))
//                .build();
//
//        HttpResponse<InputStream> response = httpClient.send(request, HttpResponse.BodyHandlers.ofInputStream());
//        if (response.statusCode() != 200) {
//            throw new IOException("Failed to get embedding. HTTP status: " + response.statusCode());
//        }
//
//        JsonObject responseMap;
//        try (InputStream bodyStream = response.body()) {
//            responseMap = Json.createReader(bodyStream).readObject();
//        }
//        JsonArray embeddingsArray = responseMap.getJsonArray("embeddings");
//        JsonArray actualEmbeddingsArray = embeddingsArray.getJsonArray(0);
//        log.info(actualEmbeddingsArray);
//
//        float[] embedding = new float[actualEmbeddingsArray.size()];
//        for (int i = 0; i < actualEmbeddingsArray.size(); i++) {
//            embedding[i] = (float) actualEmbeddingsArray.getJsonNumber(i).doubleValue();
//        }
//        long end = System.currentTimeMillis();
//        long embedTime = end - start;
//        log.info("generateEmbedding time: {} ms", embedTime);
//        return embedding;
//    }
//
//
//    /**
//     * Clean up client resources.
//     */
//    public void close() {
//        if (this.cosmosClient != null) {
//            this.cosmosClient.close();
//        }
//    }
//}