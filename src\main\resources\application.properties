# Server Configuration
server.port=8080

# Spring Boot application name
spring.application.name=pdf-uploader

# Enable file uploads
spring.servlet.multipart.enabled=true

# Maximum file size (for a single file)
spring.servlet.multipart.max-file-size=50MB

# Maximum combined size of all files in a single request
spring.servlet.multipart.max-request-size=50MB

# Benchmark Configuration
benchmark.default-top-k=10
benchmark.max-top-k=100

# Ollama Configuration
ollama.base-url=http://*************:11434
ollama.model-cache-ttl-minutes=5

