package os.tukan.extractor.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ComparisonMetrics {
    
    private double precisionDiff;
    private double recallDiff;
    private double f1Diff;
    private double ndcgDiff;
    private double mrrDiff;
    private double queryTimeDiff;
    private double embeddingTimeDiff;
}
