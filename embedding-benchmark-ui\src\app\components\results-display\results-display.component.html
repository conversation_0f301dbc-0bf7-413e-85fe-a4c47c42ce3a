<div class="card">
  <h3 class="text-lg font-medium text-gray-900 mb-4">
    <i class="fas fa-chart-bar mr-2"></i>
    Benchmark Results
  </h3>
  
  <div *ngIf="isLoading" class="text-center py-8">
    <div class="loading-spinner mx-auto mb-4"></div>
    <p class="text-gray-600">Running benchmark...</p>
    <p class="text-sm text-gray-500 mt-2">This may take several minutes</p>
  </div>
  

  <div *ngIf="!isLoading && !result" class="text-center py-8 text-gray-500">
    <i class="fas fa-chart-line text-4xl mb-4"></i>
    <p>No results yet. Configure and run a benchmark to see results.</p>
  </div>

  <div *ngIf="result?.status === BenchmarkStatus.FAILED" class="text-center py-8">
    <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
    <p class="text-red-600 font-medium">Benchmark Failed</p>
    <p class="text-sm text-red-500 mt-2">{{ result?.errorMessage }}</p>
  </div>

  <div *ngIf="result?.status === BenchmarkStatus.COMPLETED && result?.overallMetrics">
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-900 mb-3">Overall Metrics</h4>
      <div class="metrics-grid grid grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <div class="metric-card border-l-4 border-blue-400">
          <div class="text-2xl font-mono font-bold text-blue-600">
            {{ ((result?.overallMetrics?.avgPrecision ?? 0) * 100).toFixed(1) }}%
          </div>
          <div class="text-xs font-semibold text-blue-700 uppercase tracking-wide">Avg Precision</div>
        </div>
        <div class="metric-card border-l-4 border-green-400">
          <div class="text-2xl font-mono font-bold text-green-600">
            {{ ((result?.overallMetrics?.avgRecall ?? 0) * 100).toFixed(1) }}%
          </div>
          <div class="text-xs font-semibold text-green-700 uppercase tracking-wide">Avg Recall</div>
        </div>
        <div class="metric-card border-l-4 border-purple-400">
          <div class="text-2xl font-mono font-bold text-purple-600">
            {{ ((result?.overallMetrics?.avgF1 ?? 0) * 100).toFixed(1) }}%
          </div>
          <div class="text-xs font-semibold text-purple-700 uppercase tracking-wide">Avg F1</div>
        </div>
        <div class="metric-card border-l-4 border-orange-400">
          <div class="text-2xl font-mono font-bold text-orange-600">
            {{ ((result?.overallMetrics?.avgNdcg ?? 0) * 100).toFixed(1) }}%
          </div>
          <div class="text-xs font-semibold text-orange-700 uppercase tracking-wide">Avg nDCG</div>
        </div>
        <div class="metric-card border-l-4 border-indigo-400" *ngIf="result?.overallMetrics?.embeddingTimeMetrics">
          <div class="text-2xl font-mono font-bold text-indigo-600">
            {{ (result?.overallMetrics?.embeddingTimeMetrics?.avgDocumentEmbeddingTimeMs ?? 0).toFixed(0) }}ms
          </div>
          <div class="text-xs font-semibold text-indigo-700 uppercase tracking-wide">Avg Doc Embed</div>
        </div>
        <div class="metric-card border-l-4 border-pink-400" *ngIf="result?.overallMetrics?.embeddingTimeMetrics">
          <div class="text-2xl font-mono font-bold text-pink-600">
            {{ (result?.overallMetrics?.embeddingTimeMetrics?.avgQueryEmbeddingTimeMs ?? 0).toFixed(0) }}ms
          </div>
          <div class="text-xs font-semibold text-pink-700 uppercase tracking-wide">Avg Query Embed</div>
        </div>
      </div>
    </div>
    
  
    <div class="bg-gray-50 rounded-md p-4 mb-6">
      <h4 class="text-sm font-medium text-gray-900 mb-2">Execution Details</h4>
      <div class="text-sm text-gray-600 space-y-1">
        <div><strong>Model:</strong> {{ result?.modelInfo?.name }}</div>
        <div><strong>Provider:</strong> {{ result?.modelInfo?.provider }}</div>
        <div><strong>Execution Time:</strong> {{ (result?.executionTimeMs! / 1000).toFixed(1) }}s</div>
        <div><strong>Avg Query Time:</strong> {{ (result?.overallMetrics?.avgQueryTimeMs ?? 0).toFixed(0) }}ms</div>
        <div><strong>Total Queries:</strong> {{ result?.overallMetrics?.totalQueries ?? 0 }}</div>
      </div>
    </div>

    <!-- Embedding Performance Metrics -->
    <div *ngIf="result?.overallMetrics?.embeddingTimeMetrics" class="bg-blue-50 rounded-md p-4 mb-6">
      <h4 class="text-sm font-medium text-blue-900 mb-3">
        <i class="fas fa-clock mr-2"></i>
        Embedding Performance
      </h4>
      <div class="grid grid-cols-2 gap-4">
        <!-- Dataset Embedding Times -->
        <div class="bg-white rounded p-3">
          <h5 class="text-xs font-medium text-blue-800 mb-2">Dataset Processing</h5>
          <div class="text-xs text-gray-600 space-y-1">
            <div><strong>Total Time:</strong> {{ (result?.overallMetrics?.embeddingTimeMetrics?.totalDatasetEmbeddingTimeMs ?? 0).toFixed(0) }}ms</div>
            <div><strong>Avg per Document:</strong> {{ (result?.overallMetrics?.embeddingTimeMetrics?.avgDocumentEmbeddingTimeMs ?? 0).toFixed(1) }}ms</div>
            <div><strong>Documents:</strong> {{ result?.overallMetrics?.embeddingTimeMetrics?.totalDocuments ?? 0 }}</div>
            <div><strong>Range:</strong> {{ (result?.overallMetrics?.embeddingTimeMetrics?.minDocumentEmbeddingTimeMs ?? 0) }}ms - {{ (result?.overallMetrics?.embeddingTimeMetrics?.maxDocumentEmbeddingTimeMs ?? 0) }}ms</div>
          </div>
        </div>

        <!-- Query Embedding Times -->
        <div class="bg-white rounded p-3">
          <h5 class="text-xs font-medium text-blue-800 mb-2">Query Processing</h5>
          <div class="text-xs text-gray-600 space-y-1">
            <div><strong>Total Time:</strong> {{ (result?.overallMetrics?.embeddingTimeMetrics?.totalQueryEmbeddingTimeMs ?? 0).toFixed(0) }}ms</div>
            <div><strong>Avg per Query:</strong> {{ (result?.overallMetrics?.embeddingTimeMetrics?.avgQueryEmbeddingTimeMs ?? 0).toFixed(1) }}ms</div>
            <div><strong>Queries:</strong> {{ result?.overallMetrics?.embeddingTimeMetrics?.totalQueries ?? 0 }}</div>
            <div><strong>Range:</strong> {{ (result?.overallMetrics?.embeddingTimeMetrics?.minQueryEmbeddingTimeMs ?? 0) }}ms - {{ (result?.overallMetrics?.embeddingTimeMetrics?.maxQueryEmbeddingTimeMs ?? 0) }}ms</div>
          </div>
        </div>
      </div>

      <!-- Performance Summary -->
      <div class="mt-3 p-2 bg-white rounded">
        <div class="text-xs text-gray-600">
          <strong>Model Efficiency:</strong>
          {{ getEmbeddingEfficiencyText() }}
        </div>
      </div>
    </div>
    
    <div *ngIf="result?.queryResults && (result?.queryResults?.length ?? 0) > 0">
      <h4 class="text-md font-medium text-gray-900 mb-3">Query Results</h4>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Query
              </th>
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Precision
              </th>
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Recall
              </th>
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                F1
              </th>
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                nDCG
              </th>
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Embed Time
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngFor="let qr of result?.queryResults; let i = index"
                [class]="i % 2 === 0 ? 'bg-white' : 'bg-gray-50'">
              <td class="px-4 py-2 text-sm text-gray-900 max-w-xs truncate"
                  [title]="qr.query">
                {{ qr.query }}
              </td>
              <td class="px-4 py-2 text-sm text-gray-900">
                {{ (qr.precision * 100).toFixed(1) }}%
              </td>
              <td class="px-4 py-2 text-sm text-gray-900">
                {{ (qr.recall * 100).toFixed(1) }}%
              </td>
              <td class="px-4 py-2 text-sm text-gray-900">
                {{ (qr.f1 * 100).toFixed(1) }}%
              </td>
              <td class="px-4 py-2 text-sm text-gray-900">
                {{ (qr.ndcg * 100).toFixed(1) }}%
              </td>
              <td class="px-4 py-2 text-sm text-blue-600">
                {{ qr.queryEmbeddingTimeMs }}ms
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <div class="mt-6">
      <button
        (click)="downloadResults()"
        class="btn-success w-full">
        <i class="fas fa-download mr-2"></i>
        Download JSON Report
      </button>
    </div>
  </div>
</div>
