<div class="card">
  <h3 class="text-lg font-medium text-gray-900 mb-4">
    <i class="fas fa-play-circle mr-2"></i>
    Benchmark Configuration
  </h3>

  <div class="grid grid-cols-2 gap-4 mb-6">
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Similarity Function
      </label>
      <select [(ngModel)]="config.similarityFunction" class="form-select">
        <option *ngFor="let func of similarityFunctions" [value]="func">
          {{ func | titlecase }}
        </option>
      </select>
    </div>

    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Top K Results
      </label>
      <input type="number" [(ngModel)]="config.topK" min="1" max="100" class="form-input">
    </div>
  </div>

  <!-- Configuration Summary -->
  <div class="bg-gray-50 rounded-md p-4 mb-6">
    <h4 class="text-sm font-medium text-gray-900 mb-2">Configuration Summary</h4>
    <div class="text-sm text-gray-600 space-y-1">
      <div class="flex items-center">
        <i class="fas fa-brain w-4 mr-2"></i>
        <span>Model: {{ config.modelId || 'Not selected' }}</span>
        <i *ngIf="config.modelId" class="fas fa-check text-green-500 ml-2"></i>
      </div>
      <div class="flex items-center">
        <i class="fas fa-database w-4 mr-2"></i>
        <span>Dataset: {{ config.datasetId ? 'Uploaded' : 'Not uploaded' }}</span>
        <i *ngIf="config.datasetId" class="fas fa-check text-green-500 ml-2"></i>
      </div>
      <div class="flex items-center" *ngIf="config.size">
        <i class="fas fa-file-alt w-4 mr-2"></i>
        <span>Dataset size: {{ formatFileSize(config.size) }}</span>
      </div>
      <div class="flex items-center">
        <i class="fas fa-question-circle w-4 mr-2"></i>
        <span>Queries: {{ config.queries.length }} loaded</span>
        <i *ngIf="config.queries.length > 0" class="fas fa-check text-green-500 ml-2"></i>
      </div>
    </div>
  </div>

  <!-- Validation Messages -->
  <div *ngIf="!isConfigValid" class="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
    <div class="flex">
      <i class="fas fa-exclamation-triangle text-yellow-400 mr-2 mt-0.5"></i>
      <div class="text-sm text-yellow-700">
        <p class="font-medium">Configuration incomplete:</p>
        <ul class="mt-1 list-disc list-inside">
          <li *ngIf="!config.modelId">Please select an embedding model</li>
          <li *ngIf="!config.datasetId">Please upload a dataset</li>
          <li *ngIf="config.queries.length === 0">Please upload queries</li>
        </ul>
      </div>
    </div>
  </div>

  <button (click)="onStart()" [disabled]="!isConfigValid || isRunning" class="benchmark-button">
    <div *ngIf="isRunning" class="flex items-center justify-center">
      <div class="loading-spinner mr-2"></div>
      Running Benchmark...
    </div>

    <div *ngIf="!isRunning" class="flex items-center justify-center">
      <i class="fas fa-rocket mr-2"></i>
      Start Benchmark
    </div>
  </button>
</div>