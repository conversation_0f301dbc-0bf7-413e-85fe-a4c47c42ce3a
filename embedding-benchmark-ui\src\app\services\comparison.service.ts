import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  BenchmarkResult,
  ComparisonResult,
  ComparisonMetrics,
  QueryComparisonResult,
  QueryBenchmarkResult,
  RankingChange
} from '../models/benchmark.models';

@Injectable({
  providedIn: 'root'
})
export class ComparisonService {
  private comparisonResultSubject = new BehaviorSubject<ComparisonResult | null>(null);
  public comparisonResult$ = this.comparisonResultSubject.asObservable();

  private isComparisonModeSubject = new BehaviorSubject<boolean>(false);
  public isComparisonMode$ = this.isComparisonModeSubject.asObservable();

  constructor() { }

  setComparisonMode(enabled: boolean): void {
    this.isComparisonModeSubject.next(enabled);
    if (!enabled) {
      this.clearComparison();
    }
  }

  compareResults(primary: BenchmarkResult, secondary: BenchmarkResult): ComparisonResult {
    const overallComparison = this.calculateOverallComparison(primary, secondary);
    const queryComparisons = this.calculateQueryComparisons(primary, secondary);

    const comparison: ComparisonResult = {
      primary,
      secondary,
      overallComparison,
      queryComparisons
    };

    this.comparisonResultSubject.next(comparison);
    return comparison;
  }

  clearComparison(): void {
    this.comparisonResultSubject.next(null);
  }

  getCurrentComparison(): ComparisonResult | null {
    return this.comparisonResultSubject.value;
  }

  private calculateOverallComparison(primary: BenchmarkResult, secondary: BenchmarkResult): ComparisonMetrics {
    const primaryMetrics = primary.overallMetrics;
    const secondaryMetrics = secondary.overallMetrics;

    if (!primaryMetrics || !secondaryMetrics) {
      return this.createEmptyMetrics();
    }

    return {
      precisionDiff: secondaryMetrics.avgPrecision - primaryMetrics.avgPrecision,
      recallDiff: secondaryMetrics.avgRecall - primaryMetrics.avgRecall,
      f1Diff: secondaryMetrics.avgF1 - primaryMetrics.avgF1,
      ndcgDiff: secondaryMetrics.avgNdcg - primaryMetrics.avgNdcg,
      mrrDiff: secondaryMetrics.avgMrr - primaryMetrics.avgMrr,
      queryTimeDiff: secondaryMetrics.avgQueryTimeMs - primaryMetrics.avgQueryTimeMs,
      embeddingTimeDiff: this.calculateEmbeddingTimeDiff(primaryMetrics, secondaryMetrics)
    };
  }

  private calculateQueryComparisons(primary: BenchmarkResult, secondary: BenchmarkResult): QueryComparisonResult[] {
    const primaryQueries = primary.queryResults || [];
    const secondaryQueries = secondary.queryResults || [];

    const comparisons: QueryComparisonResult[] = [];

    // Create a map for quick lookup of secondary results
    const secondaryMap = new Map<string, QueryBenchmarkResult>();
    secondaryQueries.forEach(qr => secondaryMap.set(qr.query, qr));

    primaryQueries.forEach(primaryQuery => {
      const secondaryQuery = secondaryMap.get(primaryQuery.query);
      if (secondaryQuery) {
        const metrics = this.calculateQueryMetricsComparison(primaryQuery, secondaryQuery);
        const rankingChanges = this.calculateRankingChanges(primaryQuery, secondaryQuery);

        comparisons.push({
          query: primaryQuery.query,
          primary: primaryQuery,
          secondary: secondaryQuery,
          metrics,
          rankingChanges
        });
      }
    });

    return comparisons;
  }

  private calculateQueryMetricsComparison(primary: QueryBenchmarkResult, secondary: QueryBenchmarkResult): ComparisonMetrics {
    return {
      precisionDiff: secondary.precision - primary.precision,
      recallDiff: secondary.recall - primary.recall,
      f1Diff: secondary.f1 - primary.f1,
      ndcgDiff: secondary.ndcg - primary.ndcg,
      mrrDiff: secondary.mrr - primary.mrr,
      queryTimeDiff: secondary.queryTimeMs - primary.queryTimeMs,
      embeddingTimeDiff: secondary.queryEmbeddingTimeMs - primary.queryEmbeddingTimeMs
    };
  }

  private calculateRankingChanges(primary: QueryBenchmarkResult, secondary: QueryBenchmarkResult): RankingChange[] {
    const changes: RankingChange[] = [];
    const primaryRanks = new Map<string, number>();
    const secondaryRanks = new Map<string, number>();

    // Build ranking maps
    primary.retrievedDocIds.forEach((docId, index) => {
      primaryRanks.set(docId, index + 1);
    });

    secondary.retrievedDocIds.forEach((docId, index) => {
      secondaryRanks.set(docId, index + 1);
    });

    // Find all unique documents
    const allDocs = new Set([...primary.retrievedDocIds, ...secondary.retrievedDocIds]);

    allDocs.forEach(docId => {
      const primaryRank = primaryRanks.get(docId) || -1; // -1 means not found
      const secondaryRank = secondaryRanks.get(docId) || -1;

      if (primaryRank !== secondaryRank) {
        changes.push({
          documentId: docId,
          primaryRank,
          secondaryRank,
          rankDifference: secondaryRank - primaryRank
        });
      }
    });

    // Sort by absolute rank difference (most significant changes first)
    return changes.sort((a, b) => Math.abs(b.rankDifference) - Math.abs(a.rankDifference));
  }

  private calculateEmbeddingTimeDiff(primary: any, secondary: any): number | undefined {
    const primaryTime = primary.embeddingTimeMetrics?.avgQueryEmbeddingTimeMs;
    const secondaryTime = secondary.embeddingTimeMetrics?.avgQueryEmbeddingTimeMs;

    if (primaryTime !== undefined && secondaryTime !== undefined) {
      return secondaryTime - primaryTime;
    }

    return undefined;
  }

  private createEmptyMetrics(): ComparisonMetrics {
    return {
      precisionDiff: 0,
      recallDiff: 0,
      f1Diff: 0,
      ndcgDiff: 0,
      mrrDiff: 0,
      queryTimeDiff: 0,
      embeddingTimeDiff: 0
    };
  }

  // Utility methods for formatting
  formatPercentageDiff(value: number): string {
    const percentage = (value * 100).toFixed(1);
    return value >= 0 ? `+${percentage}%` : `${percentage}%`;
  }

  formatTimeDiff(value: number): string {
    const formatted = value.toFixed(1);
    return value >= 0 ? `+${formatted}ms` : `${formatted}ms`;
  }

  getMetricTrend(value: number): 'positive' | 'negative' | 'neutral' {
    if (value > 0.001) return 'positive';
    if (value < -0.001) return 'negative';
    return 'neutral';
  }

  getTimeTrend(value: number): 'positive' | 'negative' | 'neutral' {
    if (value > 1) return 'negative';
    if (value < -1) return 'positive';
    return 'neutral';
  }
}
