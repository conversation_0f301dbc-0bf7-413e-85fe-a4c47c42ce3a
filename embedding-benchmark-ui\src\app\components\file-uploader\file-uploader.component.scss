.file-upload-area {
  border: 2px dashed #a5b4fc;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 2rem 1rem;
  cursor: pointer;
  transition: border-color 0.2s, background-color 0.2s;
  text-align: center;
  position: relative;

  &:hover, &:focus {
    border-color: #6366f1;
    background-color: #eef2ff;
  }

  &.dragover {
    border-color: #2563eb;
    background-color: #e0e7ff;
  }
}

.text-center i {
  margin-bottom: 1rem;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.error, .uploading {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  margin-top: 1rem;
  display: flex;
  align-items: center;
}

.loading-spinner {
  width: 1.25rem;
  height: 1.25rem;
  border-width: 2px;
}

@media (max-width: 640px) {
  .file-upload-area {
    padding: 1rem 0.5rem;
  }
}