package os.tukan.extractor.config;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmbeddingModelConfig {
    
    private String id;
    private String name;
    private String provider;
    private String endpoint;
    private String modelName;
    private Map<String, Object> parameters = new HashMap<>();
    
    public EmbeddingModelConfig(String id, String name, String provider, String modelName) {
        this.id = id;
        this.name = name;
        this.provider = provider;
        this.modelName = modelName;
    }
}
