package os.tukan.extractor.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QueryComparisonResult {
    
    private String query;
    private QueryBenchmarkResult primary;
    private QueryBenchmarkResult secondary;
    private ComparisonMetrics metrics;
    private List<RankingChange> rankingChanges;
}
