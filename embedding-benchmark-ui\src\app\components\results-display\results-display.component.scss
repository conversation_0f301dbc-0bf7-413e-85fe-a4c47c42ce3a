.metrics-grid {
  margin-bottom: 1.5rem;
}

.metric-card {
  background: #fff;
  border-radius: 0.75rem;
  box-shadow: 0 1px 4px 0 rgba(55,65,81,0.06);
  padding: 1.25rem 1rem;
  text-align: center;
  border-left: 4px solid #e5e7eb;
  transition: box-shadow 0.2s, transform 0.1s, border-color 0.2s;

  &:hover {
    box-shadow: 0 4px 16px 0 rgba(55,65,81,0.12);
    transform: scale(1.03);
    border-color: #6366f1;
  }

  .text-2xl {
    font-family: 'Roboto Mono', monospace;
    font-weight: 700;
  }
}

.bg-gray-50, .bg-blue-50 {
  border: 1.5px solid #d1d5db;
  box-shadow: 0 2px 8px 0 rgba(55,65,81,0.04);
  padding: 1.5rem 1rem;
}

table {
  border-radius: 0.5rem;
  overflow: hidden;
  background: #fff;
}

th, td {
  vertical-align: middle;
  font-size: 0.95rem;
}

th {
  background: #f3f4f6;
  font-weight: 700;
  position: sticky;
  top: 0;
  z-index: 1;
}

tbody tr:hover {
  background-color: #f1f5f9;
}

.btn-success {
  font-size: 1.1rem;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px 0 rgba(34,197,94,0.12);
  transition: filter 0.2s, transform 0.1s;
  &:hover {
    filter: brightness(1.08);
    transform: scale(1.04);
  }
}

@media (max-width: 640px) {
  .metrics-grid {
    grid-template-columns: 1fr !important;
  }
  .metric-card {
    margin-bottom: 1rem;
  }
  table {
    font-size: 0.9rem;
  }
}
