package os.tukan.extractor.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QueryBenchmarkResult {

    private String query;
    private List<String> retrievedDocIds;
    private List<String> relevantDocIds;
    private List<Double> similarityScores;
    private double precision;
    private double recall;
    private double f1;
    private double ndcg;
    private double mrr;
    private long queryTimeMs;

    private long queryEmbeddingTimeMs;

    public QueryBenchmarkResult(String query, List<String> retrievedDocIds, List<String> relevantDocIds) {
        this.query = query;
        this.retrievedDocIds = retrievedDocIds;
        this.relevantDocIds = relevantDocIds;
        this.queryEmbeddingTimeMs = 0L;
    }
}
