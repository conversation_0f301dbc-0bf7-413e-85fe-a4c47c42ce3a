package os.tukan.extractor.service;

public interface EmbeddingProvider {
    
    /**
     * Generate embedding for the given text
     * @param text Input text to embed
     * @return Embedding vector as float array
     * @throws Exception if embedding generation fails
     */
    float[] embed(String text) throws Exception;
    
    /**
     * Check if the embedding provider is available and ready
     * @return true if available, false otherwise
     */
    boolean isAvailable();

}
