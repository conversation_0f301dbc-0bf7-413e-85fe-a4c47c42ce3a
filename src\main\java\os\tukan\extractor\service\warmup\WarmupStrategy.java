package os.tukan.extractor.service.warmup;

import os.tukan.extractor.exception.EmbeddingProviderException;
import os.tukan.extractor.service.EmbeddingProvider;

/**
 * Strategy interface for different embedding model warmup approaches.
 * Allows for pluggable warmup strategies based on model characteristics or performance requirements.
 */
public interface WarmupStrategy {

    /**
     * Performs warmup operations on the given embedding provider.
     *
     * @param provider the embedding provider to warm up
     * @param actualText the actual text that will be processed (for length-based warmup)
     * @throws EmbeddingProviderException if warmup operations fail
     */
    void performWarmup(EmbeddingProvider provider, String actualText) throws EmbeddingProviderException;

    /**
     * Returns the name of this warmup strategy for logging and identification.
     *
     * @return strategy name
     */
    String getStrategyName();

    /**
     * Returns whether this strategy is suitable for the given text length.
     *
     * @param textLength the length of text to be processed
     * @return true if this strategy is appropriate for the given text length
     */
    boolean isApplicableForTextLength(int textLength);
}
