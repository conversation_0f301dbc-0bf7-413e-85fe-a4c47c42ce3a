.comparison-manager {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;

    .title {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #374151;
    }

    .btn-toggle {
      padding: 0.5rem 1rem;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
      background: white;
      color: #374151;
      font-size: 0.875rem;
      cursor: pointer;
      transition: all 0.2s;

      &:hover:not(:disabled) {
        background: #f9fafb;
        border-color: #9ca3af;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &.active {
        background: #3b82f6;
        color: white;
        border-color: #3b82f6;

        &:hover {
          background: #2563eb;
        }
      }

      i {
        margin-right: 0.5rem;
      }
    }
  }

  .error-message {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.375rem;
    color: #dc2626;
    font-size: 0.875rem;

    i {
      margin-right: 0.5rem;
    }

    .btn-close {
      margin-left: auto;
      background: none;
      border: none;
      color: #dc2626;
      font-size: 1.25rem;
      cursor: pointer;
      padding: 0;
      width: 1.5rem;
      height: 1.5rem;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: #fee2e2;
        border-radius: 50%;
      }
    }
  }

  .no-result-message {
    display: flex;
    align-items: center;
    padding: 2rem;
    text-align: center;
    color: #6b7280;
    font-style: italic;

    i {
      margin-right: 0.5rem;
      color: #9ca3af;
    }
  }

  .current-result-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 0.5rem;
    margin-bottom: 1rem;

    .current-result-info {
      h4 {
        margin: 0 0 0.5rem 0;
        font-size: 1rem;
        font-weight: 600;
        color: #374151;
      }

      .result-name {
        margin: 0 0 0.25rem 0;
        font-weight: 500;
        color: #111827;
      }

      .result-summary {
        margin: 0;
        font-size: 0.875rem;
        color: #6b7280;
      }
    }

    .btn-save {
      padding: 0.5rem 1rem;
      background: #10b981;
      color: white;
      border: none;
      border-radius: 0.375rem;
      font-size: 0.875rem;
      cursor: pointer;
      transition: background 0.2s;

      &:hover {
        background: #059669;
      }

      i {
        margin-right: 0.5rem;
      }
    }
  }

  .comparison-controls {
    .current-result-display,
    .comparison-selector {
      margin-bottom: 1.5rem;

      h4 {
        margin: 0 0 1rem 0;
        font-size: 1rem;
        font-weight: 600;
        color: #374151;
      }
    }

    .result-card {
      padding: 1rem;
      border-radius: 0.5rem;
      border: 2px solid;

      &.primary {
        background: #eff6ff;
        border-color: #3b82f6;
      }

      &.secondary {
        background: #f0fdf4;
        border-color: #10b981;
      }

      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        .result-name {
          font-weight: 600;
          color: #111827;
        }

        .result-badge {
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;

          &.primary {
            background: #3b82f6;
            color: white;
          }

          &.secondary {
            background: #10b981;
            color: white;
          }
        }
      }

      .result-summary {
        font-size: 0.875rem;
        color: #6b7280;
      }
    }

    .loading-state {
      display: flex;
      align-items: center;
      padding: 2rem;
      text-align: center;
      color: #6b7280;

      .loading-spinner {
        width: 1rem;
        height: 1rem;
        border: 2px solid #e5e7eb;
        border-top: 2px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 0.5rem;
      }
    }

    .no-saved-results {
      text-align: center;
      padding: 2rem;
      color: #6b7280;

      i {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: #9ca3af;
      }

      p {
        margin: 0.5rem 0;
      }

      .hint {
        font-size: 0.875rem;
        font-style: italic;
      }
    }

    .result-selector {
      .form-select {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        background: white;
        margin-bottom: 1rem;

        &:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }

      .selected-result-preview {
        margin-top: 1rem;
      }
    }

    .comparison-actions {
      display: flex;
      justify-content: center;
      padding-top: 1rem;
      border-top: 1px solid #e5e7eb;

      .btn-clear {
        padding: 0.5rem 1rem;
        background: #ef4444;
        color: white;
        border: none;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: background 0.2s;

        &:hover {
          background: #dc2626;
        }

        i {
          margin-right: 0.5rem;
        }
      }
    }
  }

  .saved-results-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;

    h4 {
      margin: 0 0 1rem 0;
      font-size: 1rem;
      font-weight: 600;
      color: #374151;
    }

    .saved-results-list {
      max-height: 300px;
      overflow-y: auto;
    }

    .saved-result-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem;
      border: 1px solid #e5e7eb;
      border-radius: 0.375rem;
      margin-bottom: 0.5rem;
      background: white;

      &:hover {
        background: #f9fafb;
      }

      .result-info {
        flex: 1;

        .result-name {
          font-weight: 500;
          color: #111827;
          margin-bottom: 0.25rem;
        }

        .result-meta {
          display: flex;
          gap: 1rem;
          font-size: 0.75rem;
          color: #6b7280;

          .result-date {
            font-style: italic;
          }
        }
      }

      .result-actions {
        .btn-delete {
          padding: 0.25rem 0.5rem;
          background: #ef4444;
          color: white;
          border: none;
          border-radius: 0.25rem;
          font-size: 0.75rem;
          cursor: pointer;
          transition: background 0.2s;

          &:hover {
            background: #dc2626;
          }
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
