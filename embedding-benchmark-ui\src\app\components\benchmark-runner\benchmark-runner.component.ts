import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BenchmarkApiService } from '../../services/benchmark-api.service';
import { BenchmarkConfig } from '../../models/benchmark.models';

@Component({
  selector: 'app-benchmark-runner',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './benchmark-runner.component.html',
  styleUrls: ['./benchmark-runner.component.scss']
})
export class BenchmarkRunnerComponent implements OnInit {
  @Input() config!: BenchmarkConfig;
  @Input() isRunning = false;
  @Input() isConfigValid = false;
  @Output() startBenchmark = new EventEmitter<void>();

  similarityFunctions: string[] = [];

  constructor(private benchmarkApi: BenchmarkApiService) { }

  ngOnInit() {
    this.loadSimilarityFunctions();
  }

  private loadSimilarityFunctions() {
    this.benchmarkApi.getSimilarityFunctions().subscribe({
      next: (functions) => {
        this.similarityFunctions = functions;
      },
      error: (error) => {
        console.error('Error loading similarity functions:', error);
        this.similarityFunctions = ['cosine', 'dot_product', 'l2_norm'];
      }
    });
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  onStart() {
    if (this.isConfigValid && !this.isRunning) {
      this.startBenchmark.emit();
    }
  }
}
