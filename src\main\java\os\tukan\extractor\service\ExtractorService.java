package os.tukan.extractor.service;


import lombok.extern.log4j.Log4j2;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Log4j2
public class ExtractorService {


    private List<String> extractChunks(PDDocument document) throws IOException {
        PDFTextStripper textStripper = new PDFTextStripper();
        int numberOfPages = document.getNumberOfPages();
        List<String> chunks = new ArrayList<>();
        for (int i = 1; i <= numberOfPages; i++) {
            textStripper.setStartPage(i);
            textStripper.setEndPage(i);
            String pageText = textStripper.getText(document);
            chunks.add(pageText);
        }
        return chunks;
    }


    public String extractTextFromPdf(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IOException("File is null or empty");
        }

        try (InputStream in = file.getInputStream();
             PDDocument document = Loader.loadPDF(in.readAllBytes())) {
            List<String> chunks = extractChunks(document);
            return String.join("\n", chunks);
        } catch (IOException e) {
            throw new IOException("Failed to extract text from PDF: " + e.getMessage(), e);
        }
    }


    public String extractTextFromFile(File file) throws IOException {
        if (file == null || !file.exists()) {
            throw new IOException("File is null or does not exist");
        }
        try (PDDocument document = Loader.loadPDF(file)) {
            List<String> chunks = extractChunks(document);
            return String.join("\n", chunks);
        } catch (IOException e) {
            throw new IOException("Failed to extract text from PDF: " + e.getMessage(), e);
        }
    }


    public List<String> extractTextFromPdfWithChunk(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IOException("File is null or empty");
        }
        try (InputStream in = file.getInputStream();
             PDDocument document = Loader.loadPDF(in.readAllBytes())) {
            return extractChunks(document);
        } catch (IOException e) {
            throw new IOException("Failed to extract text from PDF: " + e.getMessage(), e);
        }
    }

    public Map<File, List<String>> extractTextFromDirectoryWithChunk(File directory) throws IOException {
        if (directory == null || !directory.exists() || !directory.isDirectory()) {
            throw new IOException("Invalid directory provided");
        }
        Map<File, List<String>> fileChunksMap = new HashMap<>();
        File[] pdfFiles = directory.listFiles((dir, name) -> name.toLowerCase().endsWith(".pdf"));
        if (pdfFiles == null || pdfFiles.length == 0) {
            throw new IOException("No PDF files found in the directory");
        }
        for (File file : pdfFiles) {
            try (PDDocument document = Loader.loadPDF(file)) {
                PDFTextStripper textStripper = new PDFTextStripper();
                int numberOfPages = document.getNumberOfPages();
                List<String> chunks = new ArrayList<>();
                for (int i = 1; i <= numberOfPages; i++) {
                    textStripper.setStartPage(i);
                    textStripper.setEndPage(i);
                    String pageText = textStripper.getText(document);
                    chunks.add(pageText);
                }
                fileChunksMap.put(file, chunks);
            } catch (IOException e) {
                throw new IOException("Failed to extract text from PDF " + file.getName() + ": " + e.getMessage(), e);
            }
        }
        return fileChunksMap;
    }
}
