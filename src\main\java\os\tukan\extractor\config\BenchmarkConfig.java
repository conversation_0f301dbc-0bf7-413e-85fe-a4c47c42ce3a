package os.tukan.extractor.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Getter
@Setter
@Component
@ConfigurationProperties("benchmark")
public class BenchmarkConfig {

    private List<String> similarityFunctions = Arrays.asList("cosine", "dot_product", "l2_norm");
    private int defaultTopK = 10;
    private int maxTopK = 100;

    public BenchmarkConfig() {
        // Default constructor for Spring
    }
}
