package os.tukan.extractor.vectorDB;


import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.json.Json;
import jakarta.json.JsonArray;
import jakarta.json.JsonObject;
import lombok.extern.log4j.Log4j2;
import org.apache.http.HttpHost;
import org.opensearch.client.RestClient;
import org.opensearch.client.json.jackson.JacksonJsonpMapper;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.ErrorCause;
import org.opensearch.client.opensearch._types.OpenSearchException;
import org.opensearch.client.opensearch.core.IndexRequest;
import org.opensearch.client.opensearch.core.IndexResponse;
import org.opensearch.client.opensearch.core.SearchResponse;
import org.opensearch.client.opensearch.core.search.Hit;
import org.opensearch.client.transport.rest_client.RestClientTransport;
import org.springframework.stereotype.Service;
import os.tukan.extractor.service.MetricsService;

import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Log4j2
@Service
public class OpenSearchVectorService {

    private static final String OPENSEARCH_URL = "http://10.10.104.126:8200"; // trailing slash optional
    private static final String INDEX = "opensearchvector";

    private final ObjectMapper objectMapper;
    private final OpenSearchClient openSearchClient;
    private final RestClient lowLevelClient;

    private final MetricsService metricsService;
    private final AtomicBoolean indexReady = new AtomicBoolean();
    private final HttpClient httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(3))
            .build();



    public OpenSearchVectorService(ObjectMapper objectMapper, MetricsService metricsService) {
        this.objectMapper = objectMapper;
        this.metricsService = metricsService;


        this.lowLevelClient = RestClient.builder(HttpHost.create(OPENSEARCH_URL)).build();
        RestClientTransport transport = new RestClientTransport(lowLevelClient, new JacksonJsonpMapper());
        this.openSearchClient = new OpenSearchClient(transport);

    }

    @PostConstruct
    public void init() throws Exception {
//        createIndexIfMissing();
    }

    private boolean indexExists() throws Exception {
        return openSearchClient.indices().exists(e -> e.index(INDEX)).value();
    }

//    private void createIndexIfMissing() throws Exception {
//        if (indexExists()) return;
//        CreateIndexRequest req = new CreateIndexRequest.Builder()
//                .index(INDEX)
//                .settings(s -> s.knn(true).numberOfShards("3").numberOfReplicas("1"))
//                .mappings(m -> m
//                        .properties("content", p -> p.text(t -> t))
//                        .properties("my_vector", p -> p.knnVector(v -> v.dimension(VECTOR_DIMENSION) )))
//                .build();
//        openSearchClient.indices().create(req);
//        log.info("Created k‑NN index '{}' (dim={})", INDEX, VECTOR_DIMENSION);
//    }




    public float[] generateEmbedding(String text) throws IOException, InterruptedException {
        /**
         *
         * other modell :
         * nomic-embed-text
         * jina/jina-embeddings-v2-base-de
         * mxbai-embed-large
         */

        String model = "nomic-embed-text";
        String host = "http://*************:11434/api/embed";
        Map<String, Object> requestBody = Map.of("model", model, "input", text);

        long start = System.currentTimeMillis();
        String jsonRequestBody = objectMapper.writeValueAsString(requestBody);
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(host))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(jsonRequestBody))
                .build();

        HttpResponse<InputStream> response = httpClient.send(request, HttpResponse.BodyHandlers.ofInputStream());
        if (response.statusCode() != 200) {
            throw new IOException("Failed to get embedding. HTTP status: " + response.statusCode());
        }

        JsonObject responseMap;
        try (InputStream bodyStream = response.body()) {
            responseMap = Json.createReader(bodyStream).readObject();
        }
        JsonArray embeddingsArray = responseMap.getJsonArray("embeddings");
        JsonArray actualEmbeddingsArray = embeddingsArray.getJsonArray(0);
        log.info(actualEmbeddingsArray);

        float[] embedding = new float[actualEmbeddingsArray.size()];
        for (int i = 0; i < actualEmbeddingsArray.size(); i++) {
            embedding[i] = (float) actualEmbeddingsArray.getJsonNumber(i).doubleValue();
        }
        long end = System.currentTimeMillis();
        long embedTime = end - start;
        log.info("generateEmbedding time: {} ms", embedTime);
        return embedding;
    }


    public void indexDocument(String id, String text, Map<String, Object> meta) throws Exception {

        long embedStart = System.currentTimeMillis();
        float[] vec = generateEmbedding(text);
        long embedTime = System.currentTimeMillis() - embedStart;
        int charCount = text.length();
        Map<String, Object> doc = new HashMap<>();
        doc.put("text", text);
        doc.put("my_vector", vec);
        if (meta != null) doc.putAll(meta);
        long storeStart = System.currentTimeMillis();

        IndexRequest<Map<String, Object>> indexRequest = IndexRequest.of(
                b -> b.index(INDEX).id(id).document(doc));

        IndexResponse res = openSearchClient.index(indexRequest);

        long storeTime = System.currentTimeMillis() - storeStart;
        log.debug("Indexed {} -> status {}", id, res.result());

        metricsService.addDocumentMetrics(id, charCount, embedTime, storeTime, embedTime + storeTime);

    }

    public List<Map<String, Object>> knnSearch(String queryText, int k) throws Exception {
        long queryStart = System.currentTimeMillis();
        float[] vec = generateEmbedding(queryText);

        try {
            SearchResponse<Map> req = openSearchClient.search(s -> s
                            .index(INDEX)
                            .query(t -> t
                                    .knn(u -> u
                                            .field("my_vector")
                                            .vector(vec)
                                            .k(k))),
                    Map.class);
            List<Map<String, Object>> results = new ArrayList<>();
            for (Hit<Map> h : req.hits().hits())
                results.add(Map.of("id", h.id(), "score", h.score(), "source", h.source().get("file_name")));
            long queryTime = System.currentTimeMillis() - queryStart;
            metricsService.addQueryTime(queryTime);
            return results;


        } catch (OpenSearchException ex) {
            ErrorCause cause = ex.error();
            log.error("Search failed: {} / {}", cause.type(), cause.reason());
            throw ex;
        }

    }
}
