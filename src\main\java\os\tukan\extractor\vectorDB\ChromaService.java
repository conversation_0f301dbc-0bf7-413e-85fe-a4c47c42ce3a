package os.tukan.extractor.vectorDB;


import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import os.tukan.extractor.service.MetricsService;
import tech.amikos.chromadb.*;
import tech.amikos.chromadb.embeddings.WithParam;
import tech.amikos.chromadb.embeddings.ollama.OllamaEmbeddingFunction;
import tech.amikos.chromadb.handler.ApiException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Log4j2
@Service
public class ChromaService {

    private final Client chromaClient;
    private final Collection collection;
    private final OllamaEmbeddingFunction embeddingFunction;
   private final MetricsService metricsService;

    public ChromaService(MetricsService metricsService) {
        this.metricsService = metricsService;

        String chromaUrl = System.getenv("CHROMA_URL");
        if (chromaUrl == null) {
            chromaUrl = "http://*************:8000";
        }
        log.info("Initializing Chroma client at: {}", chromaUrl);
        this.chromaClient = new Client(chromaUrl);
        try {
            String host = "http://*************:11434/api/embed";
            String model = "jina/jina-embeddings-v2-base-de";
            this.embeddingFunction = new OllamaEmbeddingFunction(
                    WithParam.baseAPI(host),
                    WithParam.model(model)
            );
        } catch (EFException e) {
            throw new RuntimeException("Failed to initialize OllamaEmbeddingFunction", e);
        }
        this.collection = initializeCollection(this.embeddingFunction);
    }


    private Collection initializeCollection(OllamaEmbeddingFunction embeddingFunction) {
        Collection collectionTemp;
        try {
            collectionTemp = chromaClient.createCollection(
                    "mycollection",
                    null,              // no metadata
                    false,             // createOrGet = false => throw error if collection exists
                    embeddingFunction
            );
            log.info("Successfully created new collection: {}", collectionTemp.getId());
        } catch (ApiException e) {
            try {
                collectionTemp = chromaClient.upsert("mycollection", embeddingFunction);
                log.info("Upserted existing collection '{}'.", "mycollection");
            } catch (ApiException ex2) {
                throw new RuntimeException("Failed to upsert existing collection '" + "mycollection" + "'.", ex2);
            }
        }
        return collectionTemp;
    }


    public void storeEmbedding(List<String> text, Map<String, Object> metadata, String docId) {
        if (text == null || text.isEmpty()) {
            throw new IllegalArgumentException("Text chunks cannot be null or empty");
        }

        List<String> idList = new ArrayList<>();
        for (int i = 0; i < text.size(); i++) {
            if (text.size() == 1) {
                idList.add(docId);
            } else {
                idList.add(docId + "-" + (i + 1));
            }
        }

        // Embed documents (each text chunk is embedded separately)
        List<Embedding> embeddings;
        long embedStart = System.currentTimeMillis();
        try {
            embeddings = embeddingFunction.embedDocuments(text);
            log.info("Successfully embedded {} documents.", embeddings.size());
        } catch (EFException e) {
            throw new RuntimeException("Error while embedding documents", e);
        }
        long embedTime = System.currentTimeMillis() - embedStart;

        // Transform metadata from a single map to a list (one per text chunk)
        List<Map<String, String>> metadataList = transformMetadata(metadata, text.size());

        // Store embeddings in the collection
        long storeStart = System.currentTimeMillis();
        try {
            collection.add(
                    embeddings,
                    metadataList,
                    text,
                    idList
            );
        } catch (ChromaException e) {
            throw new RuntimeException("Error storing embedding in Chroma", e);
        }
        long storeTime = System.currentTimeMillis() - storeStart;
        long charCount = text.stream().mapToInt(String::length).sum();

        metricsService.addDocumentMetrics(String.join(",", idList), charCount, embedTime, storeTime, 0);
        log.info("storeEmbedding -> file={} | embedTime={} ms | storeTime={} ms | chars={}",
                String.join(",", idList), embedTime, storeTime, charCount);
    }


    private List<Map<String, String>> transformMetadata(Map<String, Object> metadata, int count) {
        if (metadata == null) {
            return null;
        }
        Map<String, String> metaStringMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : metadata.entrySet()) {
            metaStringMap.put(entry.getKey(), entry.getValue() != null ? entry.getValue().toString() : null);
        }
        List<Map<String, String>> metadataList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            metadataList.add(new HashMap<>(metaStringMap));
        }
        return metadataList;
    }

    /**
     * Queries the collection with the provided query text and returns the response.
     *
     * @param queryText the query text
     * @param nResult the number of results to return
     * @return the query response from the collection
     * @throws ChromaException if the query fails
     */
    public Collection.QueryResponse query(String queryText, int nResult) throws ChromaException {
        if (queryText == null || queryText.isEmpty()) {
            throw new IllegalArgumentException("Query text cannot be null or empty");
        }
        List<String> documentList = List.of(queryText);
        long queryStart = System.currentTimeMillis();
        Collection.QueryResponse response = collection.query(documentList, nResult, null, null, null);
        long queryTime = System.currentTimeMillis() - queryStart;
        metricsService.addQueryTime(queryTime);

        int resultsCount = (response.getDocuments() == null ? 0 : response.getDocuments().size());
        log.info("query -> '{}' | queryTime={} ms | results={}", queryText, queryTime, resultsCount);
        return response;
    }
}
