package os.tukan.extractor.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class OllamaModelResponse {

    private List<OllamaModel> models;

    @Data
    public static class OllamaModel {
        private String name;
        private String model;

        @JsonProperty("modified_at")
        private String modifiedAt;

        private long size;
        private String digest;
        private OllamaModelDetails details;

        @Data
        public static class OllamaModelDetails {
            @JsonProperty("parent_model")
            private String parentModel;

            private String format;
            private String family;
            private List<String> families;

            @JsonProperty("parameter_size")
            private String parameterSize;

            @JsonProperty("quantization_level")
            private String quantizationLevel;
        }
    }
}
