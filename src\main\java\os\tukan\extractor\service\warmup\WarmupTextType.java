package os.tukan.extractor.service.warmup;

/**
 * Enumeration of different warmup text generation strategies.
 * Used to specify the type of warmup text to generate based on the actual input text.
 */
public enum WarmupTextType {
    
    /**
     * Generate warmup text with similar length to the actual text
     */
    SIMILAR_LENGTH,
    
    /**
     * Generate warmup text with exact length matching the actual text
     */
    EXACT_LENGTH,
    
    /**
     * Generate basic warmup text for general warming
     */
    BASIC,
    
    /**
     * Generate progressive warmup texts of increasing lengths
     */
    PROGRESSIVE
}
