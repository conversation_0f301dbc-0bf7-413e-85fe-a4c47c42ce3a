package os.tukan.extractor.vectorDB;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.HealthStatus;
import co.elastic.clients.elasticsearch.core.IndexRequest;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;

import co.elastic.clients.elasticsearch.indices.ExistsRequest;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.json.Json;
import jakarta.json.JsonArray;
import jakarta.json.JsonObject;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.springframework.stereotype.Service;
import os.tukan.extractor.service.MetricsService;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Log4j2
@Service
public class ElasticService {

    /**
     * -- GETTER --
     * Get the current index name
     */
    // Dynamic index name - will be set based on the selected model
    @Getter
    private String currentIndexName = "default-embedding-index";
    private String currentSimilarityFunction = "cosine";

    private final MetricsService metricsService;
    private final String elasticHost = "*************:9200";
    private final String host = "http://*************:11434/api/embed";

    private final JacksonJsonpMapper mapper = new JacksonJsonpMapper();

    /**
     * other modell :
     * nomic-embed-text
     * jina/jina-embeddings-v2-base-de
     * mxbai-embed-large
     */
    private final String model = "jina/jina-embeddings-v2-base-de";
    private final ObjectMapper objectMapper;
    private final HttpClient httpClient;

    private final ElasticsearchClient client;

    public ElasticService(MetricsService metricsService) {
        this.metricsService = metricsService;
        try {
            RestClient restClient = RestClient
                    .builder(HttpHost.create(elasticHost)).build();
            ElasticsearchTransport transport = new RestClientTransport(restClient, new JacksonJsonpMapper());
            this.client = new ElasticsearchClient(transport);

            this.httpClient = HttpClient.newBuilder().build();
            this.objectMapper = new ObjectMapper();

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Set the index name based on the selected model
     */
    public void setIndexName(String indexName) {
        this.currentIndexName = indexName;
    }

    public void setSimilarityFunction(String similarityFunction) {
        this.currentSimilarityFunction = similarityFunction;
    }

    public void initializeElasticsearchResources() {
        try {
            createIndexIfMissing();
        } catch (IOException e) {
            throw new RuntimeException("Failed to initialize Elasticsearch resources", e);
        }
    }

    public void createIndexIfMissing() throws IOException {
        boolean exists = client.indices().exists(ExistsRequest.of(er -> er.index(currentIndexName))).value();
        if (exists) {
            return;
        }
        createIndex();
    }

    private void createIndex() throws IOException {

        Map<String, Object> settings = Map.of(
                "number_of_shards", 1,
                "number_of_replicas", 0,
                "refresh_interval", "30s");

        Map<String, Object> mappings = new HashMap<>();

        Map<String, Object> properties = new HashMap<>();
        properties.put("content", Map.of("type", "text"));
        properties.put("filename", Map.of("type", "keyword"));
        properties.put("size", Map.of("type", "long"));
        properties.put("my_vector", Map.of(
                "type", "dense_vector",
                "index", true,
                "similarity", currentSimilarityFunction)

        );
        mappings.put("properties", properties);

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        new ObjectMapper().writeValue(baos, mappings);
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());

        ByteArrayOutputStream setOut = new ByteArrayOutputStream();
        mapper.objectMapper().writeValue(setOut, settings);

        client.indices().create(c -> c
                .index(currentIndexName)
                .settings(s -> s.withJson(new ByteArrayInputStream(setOut.toByteArray())))
                .mappings(m -> m.withJson(bais)));

        /* wait for green */
        client.cluster().health(h -> h
                .index(List.of(currentIndexName))
                .waitForStatus(HealthStatus.Green)
                .timeout(t -> t.time("30s")));

    }

    public float[] generateEmbedding(String text) throws IOException, InterruptedException {

        final int MAX_RETRIES = 2;
        final String URL = host;

        // request body
        Map<String, Object> body = Map.of("model", model, "input", text);
        String json = objectMapper.writeValueAsString(body);

        HttpRequest req = HttpRequest.newBuilder()
                .uri(URI.create(URL))
                .timeout(Duration.ofSeconds(20))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(json, StandardCharsets.UTF_8))
                .build();

        IOException last = null;
        for (int attempt = 0; true; attempt++) {
            try {
                HttpResponse<InputStream> resp = httpClient.send(req, HttpResponse.BodyHandlers.ofInputStream());

                if (resp.statusCode() != 200) {
                    throw new IOException("Embedding API HTTP " + resp.statusCode());
                }

                JsonArray vec = Json.createReader(resp.body())
                        .readObject()
                        .getJsonArray("embeddings")
                        .getJsonArray(0);

                float[] embedding = new float[vec.size()];
                for (int i = 0; i < vec.size(); i++) {
                    embedding[i] = (float) vec.getJsonNumber(i).doubleValue();
                }

                return embedding;

            } catch (IOException | InterruptedException ex) {
                last = ex instanceof IOException ? (IOException) ex : new IOException(ex);
                if (attempt == MAX_RETRIES)
                    break;

                Thread.sleep(1_000L * (attempt + 1));
            }
        }
        throw new IllegalStateException("Cannot reach embedding server at " + URL, last);
    }

    public float[] getEmbeddingFromElasticsearch(String text) throws IOException, InterruptedException {

        Map<String, Object> requestBody = Map.of(
                "input", text,
                "task_settings", Map.of(
                        "input_type", "ingest"));

        String url = "http://*************:9200/_inference/text_embedding/minilm-l6-v2-inference/";

        String jsonRequestBody = objectMapper.writeValueAsString(requestBody);

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(jsonRequestBody))
                .build();

        HttpResponse<InputStream> response = httpClient.send(request, HttpResponse.BodyHandlers.ofInputStream());
        if (response.statusCode() != 200) {
            throw new IOException("Inference API call failed: " + response.statusCode());
        }

        JsonObject responseJson;

        try (InputStream bodyStream = response.body()) {
            responseJson = Json.createReader(bodyStream).readObject();
        }

        JsonArray results = responseJson.getJsonArray("text_embedding");
        if (results == null || results.isEmpty()) {
            throw new IOException("No inference results returned.");
        }

        JsonObject embeddingObj = results.getJsonObject(0);
        JsonArray embeddingArray = embeddingObj.getJsonArray("embedding");

        float[] embedding = new float[embeddingArray.size()];
        for (int i = 0; i < embeddingArray.size(); i++) {
            embedding[i] = (float) embeddingArray.getJsonNumber(i).doubleValue();
        }

        return embedding;
    }

    public void indexDocument(String documentId, String text, float[] embedding) throws IOException {
        indexDocument(documentId, text, new HashMap<>(), embedding);
    }

    public void indexDocument(String documentId, String text, Map<String, Object> metadata, float[] embedding)
            throws IOException {
        Map<String, Object> document = new HashMap<>();
        document.put("text", text);
        document.putAll(metadata);
        document.put("my_vector", embedding);

        IndexRequest<Map<String, Object>> indexRequest = IndexRequest.of(i -> i
                .index(currentIndexName)
                .id(documentId)
                .document(document));
        client.index(indexRequest);
    }

    public void indexDocumentWithEmbedding(String documentId, String text) throws IOException, InterruptedException {
        long embedStart = System.currentTimeMillis();
        // float[] embedding = generateEmbedding(text);
        float[] elasticsearchEmbedding = getEmbeddingFromElasticsearch(text);
        long embedTime = System.currentTimeMillis() - embedStart;
        long storeStart = System.currentTimeMillis();
        indexDocument(documentId, text, elasticsearchEmbedding);
        long storeTime = System.currentTimeMillis() - storeStart;
        int charCount = text.length();
        metricsService.addDocumentMetrics(documentId, charCount, embedTime, storeTime, embedTime + storeTime);

    }

    @SuppressWarnings("rawtypes")
    public List<Map<String, Object>> knnSearch(float[] queryVector, int k) throws IOException {
        if (queryVector == null || queryVector.length == 0)
            return new ArrayList<>();

        List<Float> queryVectorList = new ArrayList<>(queryVector.length);
        for (float f : queryVector) {
            queryVectorList.add(f);
        }
        SearchRequest request = SearchRequest.of(s -> s
                .index(currentIndexName)
                .knn(knn -> knn
                        .field("my_vector")
                        .queryVector(queryVectorList)
                        .k(k)
                        .numCandidates(Math.max(100, k))));
        long startTime = System.currentTimeMillis();
        SearchResponse<Map> response = client.search(request, Map.class);
        long queryTime = System.currentTimeMillis() - startTime;
        metricsService.addQueryTime(queryTime);

        List<Map<String, Object>> results = new ArrayList<>();
        for (Hit<Map> hit : response.hits().hits()) {
            @SuppressWarnings("unchecked")
            Map<String, Object> source = (Map<String, Object>) hit.source();
            if (source == null)
                continue;
            Map<String, Object> item = new HashMap<>();
            item.put("id", hit.id());
            item.put("score", hit.score());
            results.add(item);
        }
        return results;
    }

    public void refreshIndex() throws IOException {
        client.indices().refresh(r -> r.index(currentIndexName));
    }

    @SuppressWarnings("rawtypes")
    public long getDocumentCount() throws IOException {
        SearchResponse<Map> response = client.search(s -> s
                .index(currentIndexName)
                .size(0)
                .trackTotalHits(t -> t.enabled(true)), Map.class);

        var total = response.hits().total();
        return (total != null) ? total.value() : 0;
    }
}
