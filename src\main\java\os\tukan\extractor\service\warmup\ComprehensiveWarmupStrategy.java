package os.tukan.extractor.service.warmup;

import lombok.extern.log4j.Log4j2;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import os.tukan.extractor.config.WarmupConfig;
import os.tukan.extractor.exception.EmbeddingProviderException;
import os.tukan.extractor.service.EmbeddingProvider;

/**
 * Comprehensive warmup strategy that performs multi-phase warmup to eliminate
 * cold start delays, initialization overhead, and network latency.
 */
@Log4j2
@Component
public class ComprehensiveWarmupStrategy implements WarmupStrategy {

    private final WarmupConfig warmupConfig;

    public ComprehensiveWarmupStrategy(final WarmupConfig warmupConfig) {
        this.warmupConfig = warmupConfig;
    }

    @Override
    public void performWarmup(final EmbeddingProvider provider, final String actualText) throws EmbeddingProviderException {
        if (provider == null) {
            throw new EmbeddingProviderException("Provider cannot be null for warmup");
        }
        if (actualText == null) {
            throw new EmbeddingProviderException("Actual text cannot be null for warmup");
        }

        final String providerId = provider.getClass().getSimpleName();
        MDC.put("providerId", providerId);
        MDC.put("warmupStrategy", getStrategyName());

        try {
            log.debug("Starting comprehensive warmup for provider: {}", providerId);
            final long warmupStartTime = System.nanoTime();

            // Phase 1: Basic warmup
            performBasicWarmup(provider);

            // Phase 2: Progressive warmup (if enabled)
            if (warmupConfig.isEnableProgressiveWarmup()) {
                performProgressiveWarmup(provider);
            }

            // Phase 3: Similar length warmup (if enabled)
            if (warmupConfig.isEnableSimilarLengthWarmup()) {
                performSimilarLengthWarmup(provider, actualText);
            }

            // Phase 4: Exact length warmup (if enabled)
            if (warmupConfig.isEnableExactLengthWarmup()) {
                performExactLengthWarmup(provider, actualText);
            }

            final long warmupDuration = (System.nanoTime() - warmupStartTime) / 1_000_000;
            log.info("Comprehensive warmup completed in {}ms for provider: {}", warmupDuration, providerId);

        } catch (final Exception e) {
            throw new EmbeddingProviderException("Warmup failed", providerId, "comprehensive_warmup", e);
        } finally {
            MDC.remove("providerId");
            MDC.remove("warmupStrategy");
        }
    }

    /**
     * Phase 1: Basic warmup calls to eliminate cold start effects
     */
    private void performBasicWarmup(final EmbeddingProvider provider) throws Exception {
        MDC.put("warmupPhase", "basic");
        final long phaseStart = System.nanoTime();

        try {
            for (int i = 0; i < warmupConfig.getBasicWarmupIterations(); i++) {
                final String warmupText = warmupConfig.getBaseWarmupText() + " " + i;
                provider.embed(warmupText);
            }

            if (warmupConfig.isEnableDetailedLogging()) {
                final long phaseDuration = (System.nanoTime() - phaseStart) / 1_000_000;
                log.debug("Basic warmup phase completed in {}ms", phaseDuration);
            }
        } finally {
            MDC.remove("warmupPhase");
        }
    }

    /**
     * Phase 2: Progressive length warmup to eliminate size-related initialization
     */
    private void performProgressiveWarmup(final EmbeddingProvider provider) throws Exception {
        MDC.put("warmupPhase", "progressive");
        final long phaseStart = System.nanoTime();

        try {
            for (final String warmupText : warmupConfig.getProgressiveWarmupTexts()) {
                provider.embed(warmupText);
            }

            if (warmupConfig.isEnableDetailedLogging()) {
                final long phaseDuration = (System.nanoTime() - phaseStart) / 1_000_000;
                log.debug("Progressive warmup phase completed in {}ms", phaseDuration);
            }
        } finally {
            MDC.remove("warmupPhase");
        }
    }

    /**
     * Phase 3: Similar length warmup to eliminate network latency and caching effects
     */
    private void performSimilarLengthWarmup(final EmbeddingProvider provider, final String actualText) throws Exception {
        MDC.put("warmupPhase", "similar_length");
        final long phaseStart = System.nanoTime();

        try {
            final String similarLengthText = createWarmupText(actualText, WarmupTextType.SIMILAR_LENGTH);
            provider.embed(similarLengthText);

            if (warmupConfig.isEnableDetailedLogging()) {
                final long phaseDuration = (System.nanoTime() - phaseStart) / 1_000_000;
                log.debug("Similar length warmup phase completed in {}ms", phaseDuration);
            }
        } finally {
            MDC.remove("warmupPhase");
        }
    }

    /**
     * Phase 4: Exact length warmup to eliminate any remaining overhead
     */
    private void performExactLengthWarmup(final EmbeddingProvider provider, final String actualText) throws Exception {
        MDC.put("warmupPhase", "exact_length");
        final long phaseStart = System.nanoTime();

        try {
            final String exactLengthText = createWarmupText(actualText, WarmupTextType.EXACT_LENGTH);
            provider.embed(exactLengthText);

            if (warmupConfig.isEnableDetailedLogging()) {
                final long phaseDuration = (System.nanoTime() - phaseStart) / 1_000_000;
                log.debug("Exact length warmup phase completed in {}ms", phaseDuration);
            }
        } finally {
            MDC.remove("warmupPhase");
        }
    }

    /**
     * Creates warmup text based on the specified type and actual text characteristics.
     *
     * @param actualText the actual text to base warmup text on
     * @param type the type of warmup text to create
     * @return generated warmup text
     */
    private String createWarmupText(final String actualText, final WarmupTextType type) {
        final int targetLength = Math.min(actualText.length(), warmupConfig.getMaxWarmupTextLength());

        switch (type) {
            case SIMILAR_LENGTH:
                return createSimilarLengthText(targetLength);
            case EXACT_LENGTH:
                return createExactLengthText(targetLength);
            case BASIC:
                return warmupConfig.getBaseWarmupText();
            default:
                return warmupConfig.getBaseWarmupText();
        }
    }

    private String createSimilarLengthText(final int targetLength) {
        if (targetLength <= warmupConfig.getCharacterThreshold()) {
            return warmupConfig.getBaseWarmupText() + " with similar length";
        }

        final StringBuilder sb = new StringBuilder();
        final String baseText = warmupConfig.getBaseWarmupText() + " ";
        while (sb.length() < targetLength) {
            sb.append(baseText);
        }
        return sb.substring(0, Math.min(targetLength, sb.length()));
    }

    private String createExactLengthText(final int targetLength) {
        final StringBuilder sb = new StringBuilder();
        final String pattern = "x";

        for (int i = 0; i < targetLength; i++) {
            sb.append(pattern);
        }
        return sb.toString();
    }

    @Override
    public String getStrategyName() {
        return "comprehensive";
    }

    @Override
    public boolean isApplicableForTextLength(final int textLength) {
        return textLength <= warmupConfig.getMaxWarmupTextLength();
    }
}
