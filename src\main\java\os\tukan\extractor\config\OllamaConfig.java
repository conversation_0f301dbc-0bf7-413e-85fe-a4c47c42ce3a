package os.tukan.extractor.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties("ollama")
public class OllamaConfig {
    
    private String baseUrl = "http://10.10.104.127:11434";
    private int modelCacheTtlMinutes = 5;
    
    public String getTagsEndpoint() {
        return baseUrl + "/api/tags";
    }
    
    public String getEmbedEndpoint() {
        return baseUrl + "/api/embed";
    }
}
