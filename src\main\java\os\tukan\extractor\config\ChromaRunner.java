package os.tukan.extractor.config;

import lombok.extern.log4j.Log4j2;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import os.tukan.extractor.service.ExtractorService;
import os.tukan.extractor.service.MetricsService;
import os.tukan.extractor.vectorDB.ChromaService;
import os.tukan.extractor.vectorDB.ElasticService;
import os.tukan.extractor.vectorDB.PipelineElasticSearch;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.Scanner;
import java.util.stream.Stream;

import static os.tukan.extractor.config.GroundTruthDataset.canonicalDocId;

@Log4j2
@Component

public class ChromaRunner implements CommandLineRunner {


    private final ExtractorService extractorService;
    private final ChromaService chromaService;
    private final ElasticService elasticService;
    private final PipelineElasticSearch pipelineElastic;
    private final MetricsService metrics;

    public ChromaRunner(ExtractorService extractorService,
                        ChromaService chromaService,
                        ElasticService elasticService,
                        PipelineElasticSearch pipelineElastic,
                        MetricsService metrics) {
        this.extractorService = extractorService;
        this.chromaService = chromaService;
        this.elasticService = elasticService;
        this.pipelineElastic = pipelineElastic;
        this.metrics = metrics;
    }


    private static int tryParseInt(String s, int def) {
        try {
            return Integer.parseInt(s.trim());
        } catch (Exception ignore) {
            return def;
        }
    }


    @Override
    public void run(String... args) {
        try (Scanner in = new Scanner(System.in)) {

            Backend backend = chooseBackend(in);
            Path pdfDir = askForPdfDir(in);
            int k = askForResultCount(in);

            initBackend(backend);

            ingestAllPdfs(pdfDir, backend);

            queryLoop(in, backend, k);

        } catch (Exception e) {
            log.error("Fatal error in runner", e);
        }
    }


    private Backend chooseBackend(Scanner in) {
        Map<Integer, Backend> map = Map.of(
                1, Backend.CHROMA,
                2, Backend.ELASTIC,
                3, Backend.PIPELINE_ELASTIC
        );

        System.out.println("""
                === Einbettungs-Backend wählen ===
                1: Chroma
                2: Elastic (manual embedding)
                3: Elastic ingest-pipeline
                """);
        int choice = tryParseInt(in.nextLine(), 3);
        return map.getOrDefault(choice, Backend.PIPELINE_ELASTIC);
    }

    private Path askForPdfDir(Scanner in) {
        System.out.print("path to PDF-folder: ");
        Path p = Paths.get(in.nextLine().trim());
        if (!Files.isDirectory(p)) throw new IllegalArgumentException("Folder doesn't exist: " + p);
        return p;
    }

    private int askForResultCount(Scanner in) {
        System.out.print("Number of the retrieved document (Default 10): ");
        return Math.max(1, tryParseInt(in.nextLine(), 10));
    }


    private void ingestAllPdfs(Path dir, Backend be) throws IOException {
        try (Stream<Path> files = Files.list(dir)) {

            List<Path> pdfs = files.filter(f -> f.toString().toLowerCase().endsWith(".pdf")).toList();
            log.info("Found {} PDFs. Starting ingestion via {} …", pdfs.size(), be);

            pdfs.parallelStream()
                    .forEach(pdf -> {
                        String rawId = pdf.getFileName().toString()
                                .replaceFirst("(?i)\\.pdf$", "");

                        String id = canonicalDocId(rawId);

                        try {
                            String text = extractorService.extractTextFromFile(pdf.toFile());

                            switch (be) {
                                case CHROMA -> chromaService.storeEmbedding(
                                        List.of(text), Map.of("file", pdf.getFileName().toString()), id);

                                case ELASTIC -> elasticService.indexDocumentWithEmbedding(id, text);

                                case PIPELINE_ELASTIC -> pipelineElastic.indexDocumentPipeline(
                                        id, text, Map.of("file", pdf.getFileName().toString()));
                            }

                        } catch (Exception e) {
                            log.error("❌ {} failed: {}", pdf.getFileName(), e.getMessage());
                        }
                    });

            log.info("✅  Ingestion finished.");
        }
    }


    private void queryLoop(Scanner in, Backend be, int k) {
        log.info("Query-Modus (exit = Programmende)");

        while (true) {
            System.out.print("Query: ");
            String q = in.nextLine().trim();
            if ("exit".equalsIgnoreCase(q)) break;
            if (q.isEmpty()) continue;

            try {
                switch (be) {
                    case ELASTIC -> {
                        float[] vec = elasticService.getEmbeddingFromElasticsearch(q);
                        List<Map<String, Object>> r = elasticService.knnSearch(vec, k);
                        log.info("Elastic: {} Treffer", r.size());
                    }
                    case CHROMA -> {
                        var r = chromaService.query(q, k);
                        log.info("Chroma: {} Treffer", (r.getDocuments() == null) ? 0 : r.getDocuments().size());
                    }
                    case PIPELINE_ELASTIC -> {
                        var r = pipelineElastic.semanticSearch(q, k);
                        log.info("Pipeline-Elastic: {} Treffer", r.size());
                    }
                }
            } catch (Exception e) {
                log.error("Query-Fehler: {}", e.getMessage(), e);
            }
            log.info("⏱ Ø Query-Zeit aktuell: {} ms", metrics.getAvgQueryTime());
        }

        printSummary();
    }


    private void initBackend(Backend be) throws IOException {
        switch (be) {
            case ELASTIC -> {
                log.info("Initialisiere Elastic-Ressourcen …");
                elasticService.initializeElasticsearchResources();
            }
            case PIPELINE_ELASTIC -> {
                log.info("Initialisiere Pipeline-Elastic …");
                pipelineElastic.initializeElasticsearchResources();
            }
            case CHROMA -> log.info("Chroma benötigt keine Init.");
        }
    }


    private void printSummary() {
        var docs = metrics.getDocumentDetails();
        log.info("==== Summary ({} Docs) ====", docs.size());
        log.info("Total chars: {}", metrics.getTotalEmbeddedChars());

        log.info("Avg Embedding Time (ms per doc):        {}", metrics.getAvgEmbeddingTimePerDoc());
        log.info("Avg Storage Time (ms per doc):          {}", metrics.getAvgStorageTimePerDoc());
        log.info("Avg Indexing Time (ms per doc):         {}", metrics.getAvgIndexingTimePerDoc());

        log.info("Avg Embedding Time (ms per 1000 chars): {}", metrics.getAvgEmbeddingTimePerThousandChars());
        log.info("Avg Indexing Time (ms per 1000 chars): {} ", metrics.getAvgIndexingTimePerThousandChars());
        log.info("Avg Storage Time (ms per 1000 chars):   {}", metrics.getAvgStorageTimePerThousandChars());

        log.info("Total Queries: {}", metrics.getTotalQueryCount());
        log.info("Avg Query Time (ms): {}", metrics.getAvgQueryTime());
    }


    private enum Backend {CHROMA, ELASTIC, PIPELINE_ELASTIC}
}