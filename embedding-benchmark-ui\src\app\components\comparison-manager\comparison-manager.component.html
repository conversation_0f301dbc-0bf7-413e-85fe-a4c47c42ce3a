<div class="comparison-manager">
  <div class="header">
    <h3 class="title">
      <i class="fas fa-balance-scale mr-2"></i>
      Model Comparison
    </h3>
    
    <button 
      class="btn btn-toggle"
      [class.active]="isComparisonMode"
      (click)="toggleComparisonMode()"
      [disabled]="!currentResult">
      <i class="fas" [class.fa-toggle-on]="isComparisonMode" [class.fa-toggle-off]="!isComparisonMode"></i>
      {{ isComparisonMode ? 'Exit Comparison' : 'Compare Models' }}
    </button>
  </div>

  <div *ngIf="error" class="error-message">
    <i class="fas fa-exclamation-triangle"></i>
    {{ error }}
    <button class="btn-close" (click)="error = null">×</button>
  </div>

  <div *ngIf="!currentResult" class="no-result-message">
    <i class="fas fa-info-circle"></i>
    Run a benchmark first to enable comparison mode.
  </div>

  <div *ngIf="currentResult && !isComparisonMode" class="current-result-actions">
    <div class="current-result-info">
      <h4>Current Result</h4>
      <p class="result-name">{{ getResultDisplayName(currentResult) }}</p>
      <p class="result-summary">{{ getResultSummary(currentResult) }}</p>
    </div>
    
    <button 
      class="btn btn-save"
      (click)="saveCurrentResult()"
      title="Save this result for future comparisons">
      <i class="fas fa-save"></i>
      Save Result
    </button>
  </div>

  <div *ngIf="isComparisonMode && currentResult" class="comparison-controls">
    <div class="current-result-display">
      <h4>Primary Result (Current)</h4>
      <div class="result-card primary">
        <div class="result-header">
          <span class="result-name">{{ getResultDisplayName(currentResult) }}</span>
          <span class="result-badge primary">Primary</span>
        </div>
        <div class="result-summary">{{ getResultSummary(currentResult) }}</div>
      </div>
    </div>

    <div class="comparison-selector">
      <h4>Compare Against</h4>
      
      <div *ngIf="isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <span>Loading saved results...</span>
      </div>

      <div *ngIf="!isLoading && savedResults.length === 0" class="no-saved-results">
        <i class="fas fa-folder-open"></i>
        <p>No saved results available for comparison.</p>
        <p class="hint">Save some benchmark results first to enable comparisons.</p>
      </div>

      <div *ngIf="!isLoading && savedResults.length > 0" class="result-selector">
        <select 
          class="form-select"
          [(ngModel)]="selectedResultId"
          (change)="onResultSelect()">
          <option value="">Select a result to compare...</option>
          <option 
            *ngFor="let result of savedResults" 
            [value]="result.jobId"
            [disabled]="result.jobId === currentResult.jobId">
            {{ getResultDisplayName(result) }}
          </option>
        </select>

        <div *ngIf="selectedResultId && getSelectedResult()" class="selected-result-preview">
          <div class="result-card secondary">
            <div class="result-header">
              <span class="result-name">{{ getResultDisplayName(getSelectedResult()!) }}</span>
              <span class="result-badge secondary">Secondary</span>
            </div>
            <div class="result-summary">{{ getResultSummary(getSelectedResult()!) }}</div>
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="currentComparison" class="comparison-actions">
      <button 
        class="btn btn-clear"
        (click)="clearComparison()">
        <i class="fas fa-times"></i>
        Clear Comparison
      </button>
    </div>
  </div>

  <div *ngIf="savedResults.length > 0 && !isComparisonMode" class="saved-results-section">
    <h4>Saved Results</h4>
    <div class="saved-results-list">
      <div 
        *ngFor="let result of savedResults" 
        class="saved-result-item">
        <div class="result-info">
          <div class="result-name">{{ getResultDisplayName(result) }}</div>
          <div class="result-meta">
            <span class="result-summary">{{ getResultSummary(result) }}</span>
            <span class="result-date">{{ formatDate(result.createdAt || '') }}</span>
          </div>
        </div>
        <div class="result-actions">
          <button 
            class="btn btn-delete"
            (click)="deleteResult(result.jobId)"
            title="Delete this result">
            <i class="fas fa-trash"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
