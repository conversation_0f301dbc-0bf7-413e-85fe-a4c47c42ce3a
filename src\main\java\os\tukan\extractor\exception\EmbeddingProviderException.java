package os.tukan.extractor.exception;

/**
 * Exception thrown when embedding provider operations fail.
 * Provides specific error context for embedding-related failures.
 */
public class EmbeddingProviderException extends Exception {

    private final String providerId;
    private final String operation;

    public EmbeddingProviderException(String message) {
        super(message);
        this.providerId = null;
        this.operation = null;
    }

    public EmbeddingProviderException(String message, Throwable cause) {
        super(message, cause);
        this.providerId = null;
        this.operation = null;
    }

    public EmbeddingProviderException(String message, String providerId, String operation) {
        super(String.format("Provider '%s' failed during '%s': %s", providerId, operation, message));
        this.providerId = providerId;
        this.operation = operation;
    }

    public EmbeddingProviderException(String message, String providerId, String operation, Throwable cause) {
        super(String.format("Provider '%s' failed during '%s': %s", providerId, operation, message), cause);
        this.providerId = providerId;
        this.operation = operation;
    }

    public String getProviderId() {
        return providerId;
    }

    public String getOperation() {
        return operation;
    }
}
