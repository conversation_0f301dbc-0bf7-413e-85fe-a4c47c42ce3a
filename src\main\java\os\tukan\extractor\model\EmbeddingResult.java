package os.tukan.extractor.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmbeddingResult {
    
    private float[] embedding;
    private long embeddingTimeMs;
    private String modelName;
    private String modelProvider;
    
    public EmbeddingResult(float[] embedding, long embeddingTimeMs) {
        this.embedding = embedding;
        this.embeddingTimeMs = embeddingTimeMs;
    }
}
