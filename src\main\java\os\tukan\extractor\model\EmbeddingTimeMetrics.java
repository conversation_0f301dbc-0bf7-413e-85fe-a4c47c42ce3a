package os.tukan.extractor.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmbeddingTimeMetrics {
    
    private long totalDatasetEmbeddingTimeMs;
    private double avgDocumentEmbeddingTimeMs;
    private int totalDocuments;
    private List<Long> documentEmbeddingTimes;
    
    private long totalQueryEmbeddingTimeMs;
    private double avgQueryEmbeddingTimeMs;
    private int totalQueries;
    private List<Long> queryEmbeddingTimes;
    

    private String modelName;
    private String modelProvider;
    
    private long minDocumentEmbeddingTimeMs;
    private long maxDocumentEmbeddingTimeMs;
    private long minQueryEmbeddingTimeMs;
    private long maxQueryEmbeddingTimeMs;
    
    public static EmbeddingTimeMetrics empty() {
        return new EmbeddingTimeMetrics(0L, 0.0, 0, List.of(), 0L, 0.0, 0, List.of(), 
                "", "", 0L, 0L, 0L, 0L);
    }
    
    /**
     * Calculate statistics from individual timing measurements
     */
    public void calculateStatistics() {
        // Reset all values to defaults first
        this.totalDatasetEmbeddingTimeMs = 0L;
        this.avgDocumentEmbeddingTimeMs = 0.0;
        this.totalDocuments = 0;
        this.minDocumentEmbeddingTimeMs = 0L;
        this.maxDocumentEmbeddingTimeMs = 0L;
        this.totalQueryEmbeddingTimeMs = 0L;
        this.avgQueryEmbeddingTimeMs = 0.0;
        this.totalQueries = 0;
        this.minQueryEmbeddingTimeMs = 0L;
        this.maxQueryEmbeddingTimeMs = 0L;

        if (documentEmbeddingTimes != null && !documentEmbeddingTimes.isEmpty()) {
            this.totalDatasetEmbeddingTimeMs = documentEmbeddingTimes.stream()
                    .mapToLong(Long::longValue)
                    .sum();
            this.avgDocumentEmbeddingTimeMs = (double) totalDatasetEmbeddingTimeMs / documentEmbeddingTimes.size();
            this.minDocumentEmbeddingTimeMs = documentEmbeddingTimes.stream()
                    .mapToLong(Long::longValue)
                    .min()
                    .orElse(0L);
            this.maxDocumentEmbeddingTimeMs = documentEmbeddingTimes.stream()
                    .mapToLong(Long::longValue)
                    .max()
                    .orElse(0L);
            this.totalDocuments = documentEmbeddingTimes.size();
        }
        
        if (queryEmbeddingTimes != null && !queryEmbeddingTimes.isEmpty()) {
            this.totalQueryEmbeddingTimeMs = queryEmbeddingTimes.stream()
                    .mapToLong(Long::longValue)
                    .sum();
            this.avgQueryEmbeddingTimeMs = (double) totalQueryEmbeddingTimeMs / queryEmbeddingTimes.size();
            this.minQueryEmbeddingTimeMs = queryEmbeddingTimes.stream()
                    .mapToLong(Long::longValue)
                    .min()
                    .orElse(0L);
            this.maxQueryEmbeddingTimeMs = queryEmbeddingTimes.stream()
                    .mapToLong(Long::longValue)
                    .max()
                    .orElse(0L);
            this.totalQueries = queryEmbeddingTimes.size();
        }
    }
}
