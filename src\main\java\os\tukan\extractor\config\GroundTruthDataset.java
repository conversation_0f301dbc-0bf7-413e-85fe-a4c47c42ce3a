package os.tukan.extractor.config;

import java.util.*;
import java.util.stream.Collectors;

public final class GroundTruthDataset {

    private static final Map<String, String> QUERY_KEY_EN = new HashMap<>();
    private static final Map<String, String> QUERY_KEY_DE = new HashMap<>();

    private static final Map<String, Map<String, Integer>> RAW_EN = new HashMap<>();
    private static final Map<String, Map<String, Integer>> RAW_DE = new HashMap<>();

    private static final Map<String, Map<String, Integer>> GRADED_EN = new HashMap<>();
    private static final Map<String, Map<String, Integer>> GRADED_DE = new HashMap<>();

    static {

        /* ---------- query → short key ----------------------------------- */
        QUERY_KEY_EN.put("What nutrients does the body need?", "Q1");
        QUERY_KEY_DE.put("Welche Nährstoffe braucht der menschliche Körper?", "Q1");

        QUERY_KEY_EN.put("What was the Apollo program about?", "Q2");
        QUERY_KEY_DE.put("Worum ging es beim Apollo-Programm?", "Q2");

        QUERY_KEY_EN.put("What do experts recommend for a healthy diet?", "Q3");
        QUERY_KEY_DE.put("Was empfehlen Experten für eine gesunde Ernährung?", "Q3");

        QUERY_KEY_EN.put("How is technology used in space exploration?", "Q4");
        QUERY_KEY_DE.put("Wie wird Technologie in der Raumfahrt genutzt?", "Q4");

        QUERY_KEY_EN.put("Which missions aim to return humans to the Moon?", "Q5");
        QUERY_KEY_DE.put("Welche Missionen sollen Menschen zum Mond zurückbringen?", "Q5");

        /* ---------- graded relevance: 5 (best) … 1 (weak) … 0 (none) ---- */

        RAW_EN.put("Q1", Map.of(
                "Core Human Nutrients", 5,  // 92 %
                "Government Dietary Guidelines", 4,  // 60 %
                "Plant Diet Overview", 3,  // 50 %
                "Dietary Tech Tools", 2,  // 40 %
                "Spaceflight Nutrient Planning", 1   // 10 %
        ));
        RAW_EN.put("Q2", Map.of(
                "Apollo Program Guide", 5,  // 95 %
                "Legacy of Apollo", 4,  // 90 %
                "Lunar Missions Summary", 3,  // 50 %
                "Artemis Program", 2,  // 20 %
                "Plant Diet Overview", 1   // 5 %
        ));
        RAW_EN.put("Q3", Map.of(
                "Government Dietary Guidelines", 5,
                "Core Human Nutrients", 4,
                "Plant Diet Overview", 3,
                "Dietary Tech Tools", 2,
                "Spaceflight Nutrient Planning", 1
        ));
        RAW_EN.put("Q4", Map.of(
                "AI in Space Exploration", 5,
                "Legacy of Apollo", 3,  // 40 %
                "Artemis Program", 2,  // 30 %
                "Government Dietary Guidelines", 1,  // 5 %
                "Plant Diet Overview", 0
        ));
        RAW_EN.put("Q5", Map.of(
                "Artemis Program", 5,
                "Legacy of Apollo", 4,
                "Lunar Missions Summary", 3,
                "Apollo Program Guide", 2,
                "AI in Space Exploration", 1
        ));

        RAW_DE.put("Q1", Map.of(
                "Zentrale Nährstoffe des Menschen", 5,
                "Offizielle Ernährungsempfehlungen", 4,
                "Pflanzliche Ernährung im Fokus", 3,
                "Ernährungstechnologie im Alltag", 2,
                "Ernährung im Weltraum", 1
        ));
        RAW_DE.put("Q2", Map.of(
                "Apollo-Programm erklärt", 5,
                "Apollos Vermächtnis", 4,
                "Überblick Mondmissionen", 3,
                "Artemis-Mondprogramm", 2,
                "Pflanzliche Ernährung im Fokus", 1
        ));
        RAW_DE.put("Q3", Map.of(
                "Offizielle Ernährungsempfehlungen", 5,
                "Zentrale Nährstoffe des Menschen", 4,
                "Pflanzliche Ernährung im Fokus", 3,
                "Ernährungstechnologie im Alltag", 2,
                "Ernährung im Weltraum", 1
        ));
        RAW_DE.put("Q4", Map.of(
                "KI in der Raumfahrt", 5,
                "Apollos Vermächtnis", 4,
                "Artemis-Mondprogramm", 3,
                "Offizielle Ernährungsempfehlungen", 2,
                "Pflanzliche Ernährung im Fokus", 1
        ));
        RAW_DE.put("Q5", Map.of(
                "Artemis-Mondprogramm", 5,
                "Apollos Vermächtnis", 4,
                "Überblick Mondmissionen", 3,
                "Apollo-Programm erklärt", 2,
                "KI in der Raumfahrt", 1
        ));

        RAW_EN.forEach((k, v) -> {
            Map<String, Integer> canon = new HashMap<>();
            v.forEach((d, s) -> canon.put(canonical(d), s));
            GRADED_EN.put(k, canon);
        });
        RAW_DE.forEach((k, v) -> {
            Map<String, Integer> canon = new HashMap<>();
            v.forEach((d, s) -> canon.put(canonical(d), s));
            GRADED_DE.put(k, canon);
        });
    }


    private GroundTruthDataset() {
    }

    private static String canonical(String s) {
        if (s == null) return "";
        return s
                .replaceAll("(?i)\\.pdf$", "")   // drop .pdf (case-insensitive)
                .replaceAll("[^\\p{Alnum}]", "") // remove every non-alphanumeric char
                .toLowerCase(Locale.ROOT);
    }

    public static Map<String, Integer> getGradedRelevance(String query) {
        if (query == null) return Map.of();

        String key = QUERY_KEY_EN.get(query.trim());
        if (key != null) return GRADED_EN.getOrDefault(key, Map.of());

        key = QUERY_KEY_DE.get(query.trim());
        if (key != null) return GRADED_DE.getOrDefault(key, Map.of());

        return Map.of();
    }

    public static List<Map.Entry<String,Integer>> getRelevantDocsSorted(String query) {
        return getGradedRelevance(query).entrySet().stream()
                .filter(e -> e.getValue() > 0)
                .sorted(Comparator.<Map.Entry<String,Integer>>comparingInt(Map.Entry::getValue)
                        .reversed()
                        .thenComparing(Map.Entry::getKey))
                .toList();
    }


    public static Set<String> getRelevantDocs(String query) {
        return getGradedRelevance(query).entrySet().stream()
                .filter(e -> e.getValue() > 0)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }

    public static String canonicalDocId(String docId) {
        return canonical(docId);
    }
}