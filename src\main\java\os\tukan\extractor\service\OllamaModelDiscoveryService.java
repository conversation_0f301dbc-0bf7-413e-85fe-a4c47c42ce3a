package os.tukan.extractor.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import os.tukan.extractor.config.EmbeddingModelConfig;
import os.tukan.extractor.config.OllamaConfig;
import os.tukan.extractor.dto.OllamaModelResponse;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Log4j2
@Service
public class OllamaModelDiscoveryService {

    private final OllamaConfig ollamaConfig;
    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;
    // Cache for discovered models
    private final ConcurrentHashMap<String, CachedModelList> modelCache = new ConcurrentHashMap<>();

    public OllamaModelDiscoveryService(OllamaConfig ollamaConfig) {
        this.ollamaConfig = ollamaConfig;
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Get available embedding models from Ollama with caching
     */
    public List<EmbeddingModelConfig> getAvailableEmbeddingModels() throws Exception {
        String cacheKey = "embedding_models";
        CachedModelList cached = modelCache.get(cacheKey);

        // Check if cache is valid
        if (cached != null && !cached.isExpired(ollamaConfig.getModelCacheTtlMinutes())) {
            log.debug("Returning cached embedding models");
            return cached.getModels();
        }

        // Fetch fresh models from Ollama
        List<EmbeddingModelConfig> models = fetchEmbeddingModelsFromOllama();

        // Cache the results
        modelCache.put(cacheKey, new CachedModelList(models));

        log.info("Discovered {} embedding models from Ollama", models.size());
        return models;
    }

    /**
     * Clear the model cache (useful for refresh functionality)
     */
    public void clearCache() {
        modelCache.clear();
        log.info("Ollama model cache cleared");
    }

    private List<EmbeddingModelConfig> fetchEmbeddingModelsFromOllama() throws Exception {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(ollamaConfig.getTagsEndpoint()))
                .timeout(Duration.ofSeconds(10))
                .GET()
                .build();

        try {
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200) {
                throw new Exception("Ollama API returned status: " + response.statusCode());
            }

            OllamaModelResponse ollamaResponse = objectMapper.readValue(response.body(), OllamaModelResponse.class);

            return ollamaResponse.getModels().stream()
                    .filter(this::isEmbeddingModel)
                    .map(this::convertToEmbeddingModelConfig)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Failed to fetch models from Ollama: {}", e.getMessage(), e);
            throw new Exception("Failed to connect to Ollama server: " + e.getMessage(), e);
        }
    }

    private boolean isEmbeddingModel(OllamaModelResponse.OllamaModel model) {
        return true;
    }

    private EmbeddingModelConfig convertToEmbeddingModelConfig(OllamaModelResponse.OllamaModel model) {
        String modelId = sanitizeModelName(model.getName());
        String displayName = model.getName();

        EmbeddingModelConfig config = new EmbeddingModelConfig();
        config.setId(modelId);
        config.setName(displayName);
        config.setProvider("ollama");
        config.setEndpoint(ollamaConfig.getEmbedEndpoint());
        config.setModelName(model.getName());

        // Add metadata
        config.getParameters().put("size", model.getSize());
        config.getParameters().put("modifiedAt", model.getModifiedAt());
        if (model.getDetails() != null) {
            config.getParameters().put("family", model.getDetails().getFamily());
            config.getParameters().put("parameterSize", model.getDetails().getParameterSize());
        }

        return config;
    }

    public String generateIndexName(String modelName, String similarityFunction) {
        // Extract model root (e.g., "jina" from
        // "jina/jina-embeddings-v2-base-de:latest")
        String modelRoot = "unknown";
        if (modelName != null && !modelName.isEmpty()) {
            if (modelName.contains("/")) {
                modelRoot = modelName.split("/")[0]; // take the part before the slash
            } else {
                modelRoot = modelName.split("[:\\-]")[0]; // fallback: part before colon or dash
            }
        }

        // Sanitize similarity function (remove unsafe chars, lowercase)
        String sim = similarityFunction == null ? "unknown"
                : similarityFunction.toLowerCase().replaceAll("[^a-z0-9]", "");

        return modelRoot.toLowerCase() + "-" + sim;
    }

    private String sanitizeModelName(String modelName) {
        return modelName.toLowerCase()
                .replaceAll("[^a-z0-9]", "-")
                .replaceAll("-+", "-")
                .replaceAll("^-|-$", "");
    }

    /**
     * Cache wrapper for model list with expiration
     */
    private static class CachedModelList {
        private final List<EmbeddingModelConfig> models;
        private final LocalDateTime cachedAt;

        public CachedModelList(List<EmbeddingModelConfig> models) {
            this.models = new ArrayList<>(models);
            this.cachedAt = LocalDateTime.now();
        }

        public List<EmbeddingModelConfig> getModels() {
            return new ArrayList<>(models);
        }

        public boolean isExpired(int ttlMinutes) {
            return LocalDateTime.now().isAfter(cachedAt.plusMinutes(ttlMinutes));
        }
    }
}
