package os.tukan.extractor.vectorDB;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.ElasticsearchException;
import co.elastic.clients.elasticsearch._types.HealthStatus;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.ml.*;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import lombok.extern.log4j.Log4j2;
import org.apache.http.HttpHost;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.ResponseException;
import org.elasticsearch.client.RestClient;
import org.springframework.stereotype.Service;
import os.tukan.extractor.service.MetricsService;
import co.elastic.clients.elasticsearch.ml.TrainedModelDeploymentStats;
import co.elastic.clients.elasticsearch.ml.DeploymentAllocationState;
import co.elastic.clients.elasticsearch.ml.DeploymentAssignmentState;
import co.elastic.clients.elasticsearch.ml.TrainedModelStats;


import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;


@Log4j2
@Service
public class PipelineElasticSearch {


    private static final String ES_URL = "http://*************:9200";
    private static final String MODEL_ID = "intfloat__multilingual-e5-large";
    private static final String PIPELINE_ID = "embedded_pipeline2";
    private static final String INDEX_NAME = "sentence_transformers2";


    private final MetricsService metrics;
    private final RestClient restClient;
    private final ElasticsearchClient esClient;

    private final JacksonJsonpMapper mapper = new JacksonJsonpMapper();

    public PipelineElasticSearch(MetricsService metrics) {
        this.metrics = metrics;
        restClient = RestClient.builder(HttpHost.create(ES_URL)).build();
        ElasticsearchTransport tr = new RestClientTransport(restClient, mapper);
        this.esClient = new ElasticsearchClient(tr);
    }

    public void initializeElasticsearchResources() throws IOException {
        try {
            ensureModelDeployed();
            createIndexIfMissing();
            createPipelineIfMissing();


        } catch (IOException e) {
            throw new IllegalStateException("Elastic init failed", e);
        }
    }


    private void ensureModelDeployed() throws IOException {

        GetTrainedModelsStatsResponse stats = esClient.ml()
                .getTrainedModelsStats(r -> r.modelId(MODEL_ID));

        boolean alreadyRunning = stats.trainedModelStats().stream()
                .map(TrainedModelStats::deploymentStats)
                .filter(Objects::nonNull)
                .map(TrainedModelDeploymentStats::state)
                .anyMatch(s -> s == DeploymentAssignmentState.Started);

        if (alreadyRunning) {
            log.info("Model [{}] is already deployed.", MODEL_ID);
            return;
        }

        log.info("Deploying model [{}] …", MODEL_ID);

        esClient.ml().startTrainedModelDeployment(req -> req
                .modelId(MODEL_ID)
                .waitFor(DeploymentAllocationState.FullyAllocated)
                .timeout(t -> t.time("2m"))
        );

        log.info("Model [{}] deployment finished.", MODEL_ID);
    }


    private void createIndexIfMissing() throws IOException {
        if (esClient.indices().exists(r -> r.index(INDEX_NAME)).value()) {
            log.info("Index '{}' already exists.", INDEX_NAME);
            return;
        }

        log.info("Creating index '{}' …", INDEX_NAME);

        Map<String, Object> mappings = Map.of(
                "properties", Map.of(
                        "language", Map.of("type", "keyword"),      // new
                        "source_code", Map.of("type", "text"),                        "my_vector", Map.of(
                                "properties", Map.of(
                                        "predicted_value", Map.of(
                                                "type", "dense_vector",
                                                "index", true,
                                                "similarity", "cosine"
                                        )
                                )
                        )
                )
        );

        ByteArrayOutputStream mapOut = new ByteArrayOutputStream();
        mapper.objectMapper().writeValue(mapOut, mappings);

        esClient.indices().create(c -> c
                .index(INDEX_NAME)
                .settings(s -> s
                        .numberOfShards("1")
                        .numberOfReplicas("0")
                        .refreshInterval(t -> t.time("30s")))
                .mappings(m -> m.withJson(new ByteArrayInputStream(mapOut.toByteArray())))
        );

        esClient.cluster().health(h -> h
                .index(List.of(INDEX_NAME))
                .waitForStatus(HealthStatus.Green)
                .timeout(t -> t.time("30s"))
        );
        log.info("Index '{}' is GREEN.", INDEX_NAME);
    }


    private void createPipelineIfMissing() throws IOException {
        if (pipelineExists()) {
            log.info("Pipeline '{}' already exists.", PIPELINE_ID);
            return;
        }
        String json = """
                {
                  "description": "E5-small embedding pipeline",
                  "processors": [
                    {
                      "inference": {
                        "model_id": "%s",
                        "field_map":   { "text": "text" },
                        "target_field": "my_vector",
                        "inference_config": { "text_embedding": {} }
                      }
                    }
                  ]
                }
                """.formatted(MODEL_ID);

        Request put = new Request("PUT", "/_ingest/pipeline/" + PIPELINE_ID);
        put.setJsonEntity(json);
        restClient.performRequest(put);
        log.info("Pipeline '{}' created.", PIPELINE_ID);
    }

    private boolean pipelineExists() throws IOException {
        try {
            restClient.performRequest(new Request("GET", "/_ingest/pipeline/" + PIPELINE_ID));
            return true;
        } catch (ResponseException e) {
            if (e.getResponse().getStatusLine().getStatusCode() == 404) return false;
            throw e;
        }
    }


    public void indexDocumentPipeline(String id, String text, Map<String, Object> meta) throws IOException {

        long t0 = System.currentTimeMillis();
        Map<String, Object> doc = new HashMap<>();
        doc.put("text_field", text);
        if (meta != null) doc.putAll(meta);

        try {
            esClient.index(i -> i
                    .index(INDEX_NAME)
                    .id(id)
                    .document(doc)
                    .pipeline(PIPELINE_ID)
            );
            long t1 = System.currentTimeMillis();
            long indexTime = t1 - t0;
            long charCount = text.length();
            metrics.addDocumentMetrics(id, charCount, 0, 0,indexTime);



        } catch (ElasticsearchException ex) {
            if (ex.status() == 409 && ex.getMessage().contains("model assignment has been removed")) {
                log.warn("Model assignment lost – auto-deploying and retrying …");

                esClient.index(i -> i.index(INDEX_NAME).id(id).document(doc).pipeline(PIPELINE_ID));
            } else throw ex;
        }


    }


    public List<Map<String, Object>> semanticSearch(String query, int k) throws IOException {

        long t0 = System.currentTimeMillis();

        SearchResponse<Map> resp = esClient.search(s -> s
                .index(INDEX_NAME)
                .knn(knn -> knn
                        .field("my_vector.predicted_value")
                        .k(k)
                        .numCandidates(Math.max(k * 50, 200))
                        .queryVectorBuilder(qv -> qv
                                .textEmbedding(te -> te
                                        .modelId(MODEL_ID)
                                        .modelText(query)
                                )
                        )
                ), Map.class);

        long t1 = System.currentTimeMillis();
        metrics.addQueryTime(t1 - t0);

        log.info(resp.hits());
        List<Map<String, Object>> out = new ArrayList<>();
        for (Hit<Map> h : resp.hits().hits()) {
            out.add(Map.of("id", h.id(), "score", h.score()));
        }
        return out;
    }
}