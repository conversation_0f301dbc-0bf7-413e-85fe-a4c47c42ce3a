package os.tukan.extractor.service;

import lombok.extern.log4j.Log4j2;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import os.tukan.extractor.config.EmbeddingModelConfig;
import os.tukan.extractor.config.WarmupConfig;
import os.tukan.extractor.exception.ConfigurationException;
import os.tukan.extractor.exception.EmbeddingProviderException;
import os.tukan.extractor.model.EmbeddingResult;
import os.tukan.extractor.service.EmbeddingProvider;
import os.tukan.extractor.service.OllamaEmbeddingProvider;
import os.tukan.extractor.service.warmup.WarmupStrategy;

import javax.annotation.PreDestroy;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Optimized embedding model manager with comprehensive warmup, thread safety, and enhanced observability.
 *
 * Features:
 * - Thread-safe provider caching with ConcurrentHashMap
 * - Configurable warmup strategies for optimal performance
 * - Comprehensive input validation and error handling
 * - Structured logging with MDC context
 * - Cache eviction policies to prevent memory leaks
 * - Statistical timing accuracy with multiple samples
 *
 * Thread Safety: This class is thread-safe and can be used concurrently.
 * Cache Behavior: Providers are cached per configuration ID with automatic eviction.
 * Performance: Comprehensive warmup eliminates cold start delays and network latency.
 */
@Log4j2
@Service
public class EmbeddingModelManager {

    // Constants for configuration and cache management
    private static final String CACHE_KEY_PREFIX = "provider_";
    private static final String MDC_CONFIG_ID = "configId";
    private static final String MDC_MODEL_NAME = "modelName";
    private static final String MDC_PROVIDER_TYPE = "providerType";
    private static final int DEFAULT_MAX_CACHE_SIZE = 100;
    private static final Duration DEFAULT_CACHE_TTL = Duration.ofHours(1);

    // Thread-safe provider cache with eviction policy
    private final ConcurrentHashMap<String, CachedProvider> providerCache = new ConcurrentHashMap<>();

    // Cache metrics for observability
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    private final AtomicLong providerCreations = new AtomicLong(0);

    // Configuration dependencies
    private final WarmupConfig warmupConfig;
    private final WarmupStrategy warmupStrategy;

    /**
     * Default constructor for backward compatibility.
     * Initializes without warmup capabilities.
     */
    public EmbeddingModelManager() {
        this.warmupConfig = null;
        this.warmupStrategy = null;
        log.info("EmbeddingModelManager initialized without warmup (backward compatibility mode)");
    }

    /**
     * Constructor with dependency injection for configuration and warmup strategy.
     * Both dependencies are optional - if not available, warmup will be skipped.
     *
     * @param warmupConfig configuration for warmup parameters (optional)
     * @param warmupStrategy strategy for performing warmup operations (optional)
     */
    public EmbeddingModelManager(final WarmupConfig warmupConfig, final WarmupStrategy warmupStrategy) {
        this.warmupConfig = warmupConfig;
        this.warmupStrategy = warmupStrategy;

        if (warmupConfig != null && warmupStrategy != null) {
            log.info("EmbeddingModelManager initialized with warmup strategy: {}", warmupStrategy.getStrategyName());
        } else {
            log.warn("EmbeddingModelManager initialized without warmup (config: {}, strategy: {})",
                    warmupConfig != null, warmupStrategy != null);
        }
    }

    /**
     * Generates embedding for the given text without timing measurements.
     * Includes comprehensive input validation and error handling.
     *
     * @param text the text to generate embedding for (must not be null or empty)
     * @param config the embedding model configuration (must not be null)
     * @return embedding vector as float array
     * @throws ConfigurationException if configuration is invalid
     * @throws EmbeddingProviderException if embedding generation fails
     */
    public float[] generateEmbeddingOnly(final String text, final EmbeddingModelConfig config)
            throws ConfigurationException, EmbeddingProviderException {

        validateInputParameters(text, config);

        final EmbeddingProvider provider = getProvider(config);

        try {
            return provider.embed(text);
        } catch (final Exception e) {
            throw new EmbeddingProviderException("Embedding generation failed", config.getId(), "embed", e);
        }
    }

    /**
     * Gets an embedding provider for the given model configuration with thread-safe caching.
     * Uses computeIfAbsent for atomic cache operations and prevents duplicate provider creation.
     *
     * @param config the embedding model configuration (must not be null)
     * @return cached or newly created embedding provider
     * @throws ConfigurationException if configuration is invalid
     */
    public EmbeddingProvider getProvider(final EmbeddingModelConfig config) throws ConfigurationException {
        validateConfiguration(config);

        final String cacheKey = generateCacheKey(config);

        // Use computeIfAbsent for thread-safe atomic cache operations
        final CachedProvider cachedProvider = providerCache.computeIfAbsent(cacheKey, key -> {
            cacheMisses.incrementAndGet();
            return createCachedProvider(config);
        });

        // Check if cached provider is still valid
        if (cachedProvider.isExpired()) {
            providerCache.remove(cacheKey);
            return getProvider(config); // Recursive call to create new provider
        }

        cacheHits.incrementAndGet();
        logCacheMetrics();

        return cachedProvider.getProvider();
    }


    /**
     * Tests connection to the embedding model with comprehensive error handling.
     *
     * @param config the embedding model configuration to test
     * @return true if the model is available and responsive, false otherwise
     */
    public boolean testModelConnection(final EmbeddingModelConfig config) {
        if (config == null) {
            log.warn("Cannot test connection with null config");
            return false;
        }

        MDC.put(MDC_CONFIG_ID, config.getId());
        MDC.put(MDC_MODEL_NAME, config.getModelName());

        try {
            final EmbeddingProvider provider = getProvider(config);
            final boolean isAvailable = provider.isAvailable();

            log.debug("Model connection test for {}: {}", config.getModelName(),
                    isAvailable ? "SUCCESS" : "FAILED");

            return isAvailable;
        } catch (final Exception e) {
            log.warn("Model connection test failed for {}: {}", config.getModelName(), e.getMessage());
            return false;
        } finally {
            MDC.remove(MDC_CONFIG_ID);
            MDC.remove(MDC_MODEL_NAME);
        }
    }

    /**
     * Generates embedding with comprehensive timing measurements and warmup.
     *
     * Features:
     * - Comprehensive warmup to eliminate cold start delays
     * - Multiple timing samples for statistical accuracy
     * - Median timing calculation to reduce outlier impact
     * - Structured logging with performance metrics
     *
     * @param text the text to generate embedding for (must not be null or empty)
     * @param config the embedding model configuration (must not be null)
     * @return embedding result with accurate timing information
     * @throws ConfigurationException if configuration is invalid
     * @throws EmbeddingProviderException if embedding generation fails
     */
    public EmbeddingResult generateEmbeddingWithTiming(final String text, final EmbeddingModelConfig config)
            throws ConfigurationException, EmbeddingProviderException {

        validateInputParameters(text, config);

        MDC.put(MDC_CONFIG_ID, config.getId());
        MDC.put(MDC_MODEL_NAME, config.getModelName());
        MDC.put(MDC_PROVIDER_TYPE, config.getProvider());

        try {
            final EmbeddingProvider provider = getProvider(config);

            // Perform comprehensive warmup if strategy is available and applicable
            if (warmupStrategy != null && warmupConfig != null) {
                try {
                    if (warmupStrategy.isApplicableForTextLength(text.length())) {
                        performWarmupWithTiming(provider, text);
                    } else {
                        log.debug("Warmup strategy not applicable for text length: {}", text.length());
                    }
                } catch (final Exception e) {
                    log.warn("Warmup failed, continuing without warmup: {}", e.getMessage());
                    // Continue without warmup - don't fail the entire embedding generation
                }
            } else {
                log.debug("Warmup not available (strategy: {}, config: {})",
                        warmupStrategy != null, warmupConfig != null);
            }

            // Generate embeddings with multiple timing samples for statistical accuracy
            final TimingResult timingResult = generateEmbeddingsWithMultipleSamples(provider, text);

            log.info("Embedding generation completed - Model: {}, Time: {}ms, Samples: {}",
                    config.getModelName(), timingResult.getMedianTime(),
                    warmupConfig != null ? warmupConfig.getTimingSamples() : 3);

            return new EmbeddingResult(timingResult.getEmbedding(), timingResult.getMedianTime(),
                    config.getModelName(), config.getProvider());

        } catch (final EmbeddingProviderException e) {
            throw e; // Re-throw as-is
        } catch (final Exception e) {
            throw new EmbeddingProviderException("Embedding generation with timing failed",
                    config.getId(), "generateEmbeddingWithTiming", e);
        } finally {
            MDC.remove(MDC_CONFIG_ID);
            MDC.remove(MDC_MODEL_NAME);
            MDC.remove(MDC_PROVIDER_TYPE);
        }
    }

    /**
     * Performs warmup with timing measurements for observability.
     *
     * @param provider the embedding provider
     * @param text the actual text to be processed
     * @throws EmbeddingProviderException if warmup fails
     */
    private void performWarmupWithTiming(final EmbeddingProvider provider, final String text)
            throws EmbeddingProviderException {
        final long warmupStart = System.nanoTime();
        final String providerId = provider.getClass().getSimpleName();

        try {
            warmupStrategy.performWarmup(provider, text);

            final long warmupDuration = (System.nanoTime() - warmupStart) / 1_000_000;
            log.debug("Warmup completed in {}ms using strategy: {}", warmupDuration, warmupStrategy.getStrategyName());

        } catch (final Exception e) {
            log.error("Warmup failed for provider {}: {}", providerId, e.getMessage(), e);
            throw new EmbeddingProviderException("Warmup failed", providerId, "warmup", e);
        }
    }

    /**
     * Generates embeddings with multiple timing samples for statistical accuracy.
     *
     * @param provider the embedding provider
     * @param text the text to process
     * @return timing result with embedding and median time
     * @throws Exception if embedding generation fails
     */
    private TimingResult generateEmbeddingsWithMultipleSamples(final EmbeddingProvider provider, final String text)
            throws Exception {
        final List<Long> timings = new ArrayList<>();
        float[] finalEmbedding = null;

        // Take multiple timing measurements to reduce variance
        final int timingSamples = warmupConfig != null ? warmupConfig.getTimingSamples() : 3;
        for (int i = 0; i < timingSamples; i++) {
            final long start = System.nanoTime();
            final float[] embedding = provider.embed(text);
            final long durationMs = (System.nanoTime() - start) / 1_000_000;
            timings.add(durationMs);

            if (i == 0) {
                finalEmbedding = embedding; // Use first embedding as result
            }
        }

        final long medianTime = calculateMedianTiming(timings);
        return new TimingResult(finalEmbedding, medianTime);
    }

    /**
     * Calculates median timing from a list of timing measurements.
     * Uses median instead of average to reduce impact of outliers.
     *
     * @param timings list of timing measurements in milliseconds
     * @return median timing value
     */
    private long calculateMedianTiming(final List<Long> timings) {
        if (timings == null || timings.isEmpty()) {
            return 0L;
        }

        final List<Long> sortedTimings = new ArrayList<>(timings);
        sortedTimings.sort(Long::compareTo);

        final int size = sortedTimings.size();
        if (size % 2 == 0) {
            return (sortedTimings.get(size / 2 - 1) + sortedTimings.get(size / 2)) / 2;
        } else {
            return sortedTimings.get(size / 2);
        }
    }


    /**
     * Validates input parameters for embedding generation.
     *
     * @param text the text to validate
     * @param config the configuration to validate
     * @throws ConfigurationException if validation fails
     */
    private void validateInputParameters(final String text, final EmbeddingModelConfig config)
            throws ConfigurationException {
        if (text == null) {
            throw new ConfigurationException("Text cannot be null", "text", null);
        }
        if (text.trim().isEmpty()) {
            throw new ConfigurationException("Text cannot be empty", "text", text);
        }
        if (warmupConfig != null && text.length() > warmupConfig.getMaxWarmupTextLength()) {
            throw new ConfigurationException("Text length exceeds maximum allowed", "text.length", text.length());
        }

        validateConfiguration(config);
    }

    /**
     * Validates embedding model configuration.
     *
     * @param config the configuration to validate
     * @throws ConfigurationException if validation fails
     */
    private void validateConfiguration(final EmbeddingModelConfig config) throws ConfigurationException {
        if (config == null) {
            throw new ConfigurationException("Embedding model config cannot be null", "config", null);
        }
        if (config.getId() == null || config.getId().trim().isEmpty()) {
            throw new ConfigurationException("Config ID cannot be null or empty", "config.id", config.getId());
        }
        if (config.getProvider() == null || config.getProvider().trim().isEmpty()) {
            throw new ConfigurationException("Provider cannot be null or empty", "config.provider", config.getProvider());
        }
        if (config.getModelName() == null || config.getModelName().trim().isEmpty()) {
            throw new ConfigurationException("Model name cannot be null or empty", "config.modelName", config.getModelName());
        }
    }

    /**
     * Generates cache key for the given configuration.
     *
     * @param config the configuration
     * @return cache key
     */
    private String generateCacheKey(final EmbeddingModelConfig config) {
        return CACHE_KEY_PREFIX + config.getId();
    }

    /**
     * Creates a new cached provider for the given configuration.
     *
     * @param config the configuration
     * @return cached provider wrapper
     */
    private CachedProvider createCachedProvider(final EmbeddingModelConfig config) {
        providerCreations.incrementAndGet();

        MDC.put(MDC_CONFIG_ID, config.getId());
        MDC.put(MDC_MODEL_NAME, config.getModelName());
        MDC.put(MDC_PROVIDER_TYPE, config.getProvider());

        try {
            final EmbeddingProvider provider = createProvider(config);
            log.info("Created new embedding provider: {} for model: {}", config.getProvider(), config.getModelName());
            return new CachedProvider(provider, Instant.now().plus(DEFAULT_CACHE_TTL));
        } finally {
            MDC.remove(MDC_CONFIG_ID);
            MDC.remove(MDC_MODEL_NAME);
            MDC.remove(MDC_PROVIDER_TYPE);
        }
    }

    /**
     * Creates a new embedding provider based on configuration.
     *
     * @param config the configuration
     * @return embedding provider
     * @throws IllegalArgumentException if provider type is unsupported
     */
    private EmbeddingProvider createProvider(final EmbeddingModelConfig config) {
        final String providerType = config.getProvider().toLowerCase();

        switch (providerType) {
            case "ollama":
                final OllamaEmbeddingProvider ollamaProvider = new OllamaEmbeddingProvider();
                ollamaProvider.setConfig(config);
                return ollamaProvider;
            default:
                throw new IllegalArgumentException("Unsupported embedding provider: " + config.getProvider());
        }
    }

    /**
     * Logs cache metrics for observability.
     */
    private void logCacheMetrics() {
        final long hits = cacheHits.get();
        final long misses = cacheMisses.get();
        final long total = hits + misses;

        if (total > 0 && total % 100 == 0) { // Log every 100 cache operations
            final double hitRatio = (double) hits / total * 100;
            log.info("Cache metrics - Hits: {}, Misses: {}, Hit Ratio: {:.2f}%, Cache Size: {}, Providers Created: {}",
                    hits, misses, hitRatio, providerCache.size(), providerCreations.get());
        }
    }

    /**
     * Clears the provider cache and resets metrics.
     * Useful for testing and cache refresh operations.
     */
    public void clearCache() {
        final int cacheSize = providerCache.size();
        providerCache.clear();
        cacheHits.set(0);
        cacheMisses.set(0);
        providerCreations.set(0);

        log.info("Provider cache cleared. Removed {} cached providers", cacheSize);
    }

    /**
     * Cleanup method called when the bean is destroyed.
     * Ensures proper resource cleanup.
     */
    @PreDestroy
    public void cleanup() {
        log.info("EmbeddingModelManager cleanup - Final cache size: {}, Total hits: {}, Total misses: {}",
                providerCache.size(), cacheHits.get(), cacheMisses.get());
        clearCache();
    }

    /**
     * Cache wrapper for embedding providers with expiration support.
     */
    private static class CachedProvider {
        private final EmbeddingProvider provider;
        private final Instant expiresAt;

        public CachedProvider(final EmbeddingProvider provider, final Instant expiresAt) {
            this.provider = Objects.requireNonNull(provider, "Provider cannot be null");
            this.expiresAt = Objects.requireNonNull(expiresAt, "Expiration time cannot be null");
        }

        public EmbeddingProvider getProvider() {
            return provider;
        }

        public boolean isExpired() {
            return Instant.now().isAfter(expiresAt);
        }
    }

    /**
     * Result wrapper for timing measurements with embedding data.
     */
    private static class TimingResult {
        private final float[] embedding;
        private final long medianTime;

        public TimingResult(final float[] embedding, final long medianTime) {
            this.embedding = Objects.requireNonNull(embedding, "Embedding cannot be null");
            this.medianTime = medianTime;
        }

        public float[] getEmbedding() {
            return embedding;
        }

        public long getMedianTime() {
            return medianTime;
        }
    }
}
