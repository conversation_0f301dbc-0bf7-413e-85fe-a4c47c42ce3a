package os.tukan.extractor.controller;

import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/import")
public class ImporterController {
    private final RestTemplate restTemplate = new RestTemplate();


    @PostMapping(value = "/uploadSingle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> uploadSinglePdf(@RequestPart("cid_pdf") MultipartFile pdfFile) {
        try {
            if (pdfFile.isEmpty()) {
                return ResponseEntity.badRequest().body("No file was provided.");
            }

            File tempFile = convertMultiPartToFile(pdfFile);
            ResponseEntity<String> response = sendPdfToDms(tempFile);
            tempFile.delete();

            return ResponseEntity.status(response.getStatusCode()).body(response.getBody());

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Error sending PDF: " + e.getMessage());
        }
    }

    private File convertMultiPartToFile(MultipartFile file) throws Exception {
        File convFile = File.createTempFile("upload_", ".pdf");
        file.transferTo(convFile);
        return convFile;
    }

    @PostMapping("/uploadAll")
    public ResponseEntity<?> uploadAll() {

        String path = "C:/Users/<USER>/Desktop/datachuck/qdoc/documents/";
        File directory = new File(path);

        if (!directory.exists() || !directory.isDirectory()) {
            return ResponseEntity.badRequest().body("Invalid directory path: " + path);
        }

        File[] files = directory.listFiles();
        if (files == null) {
            return ResponseEntity.badRequest().body("Unable to list files in: " + path);
        }

        List<Map<String, Object>> results = new ArrayList<>();

        for (File file : files) {
            if (file.isFile() && file.getName().toLowerCase().endsWith(".pdf")) {
                try {
                    ResponseEntity<String> response = sendPdfToDms(file);

                    // Record success info
                    Map<String, Object> result = new HashMap<>();
                    result.put("file", file.getName());
                    result.put("status", response.getStatusCode());
                    result.put("responseBody", response.getBody());
                    results.add(result);
                } catch (Exception e) {
                    // Record error info
                    Map<String, Object> error = new HashMap<>();
                    error.put("file", file.getName());
                    error.put("error", e.getMessage());
                    results.add(error);
                }
            }
        }
        // Return the list of results
        return ResponseEntity.ok(results);
    }


    private ResponseEntity<String> sendPdfToDms(File pdfFile) throws Exception {
        byte[] fileContent = Files.readAllBytes(pdfFile.toPath());
        String dmsUrl = "http://10.10.104.127:7981/objects";


        try {

        String jsonMetadata = """
        {
          "objects": [
            {
              "properties": {
                "system:objectTypeId": { "value": "262170" },
                "system:parentId":     { "value": "3475" },
                "Text0":               { "value": "%s" }
              },
              "contentStreams": [
                {
                  "mimeType": "application/pdf",
                  "fileName": "%s",
                  "cid": "cid_pdf"
                }
              ]
            }
          ]
        }
        """.formatted(pdfFile.getName(), pdfFile.getName());

        MultiValueMap<String, Object> multipartBody = new LinkedMultiValueMap<>();

        HttpHeaders jsonHeaders = new HttpHeaders();
        jsonHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> jsonPart = new HttpEntity<>(jsonMetadata, jsonHeaders);
        multipartBody.add("data", jsonPart);

        HttpHeaders pdfHeaders = new HttpHeaders();
        pdfHeaders.setContentType(MediaType.APPLICATION_PDF);
        ContentDisposition cd = ContentDisposition
                .builder("form-data")
                .name("cid_pdf") // Must match the "cid" in the JSON
                .filename(pdfFile.getName())
                .build();
        pdfHeaders.setContentDisposition(cd);

        HttpEntity<byte[]> pdfPart = new HttpEntity<>(fileContent, pdfHeaders);
        multipartBody.add("cid_pdf", pdfPart);

        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth("administrator", "optimal");
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(multipartBody, headers);


        ResponseEntity<String> response = restTemplate.postForEntity(dmsUrl, requestEntity, String.class);
        return ResponseEntity.status(response.getStatusCode()).body(response.getBody());

    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error processing PDF file: " + e.getMessage());
    }
}
}
