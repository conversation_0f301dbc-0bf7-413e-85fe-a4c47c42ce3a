.benchmark-button {
  background-color: #374151; // dark gray
  color: white;
  padding: 0.6rem 1.2rem;
  border-radius: 0.5rem;
  width: 100%;
  max-width: 220px;
  margin: 1.5rem auto 0 auto;
  display: block;
  font-weight: 600;
  box-shadow: 0 2px 8px 0 rgba(55,65,81,0.08);
  transition: background 0.2s, transform 0.1s, box-shadow 0.2s;

  &:hover:not(:disabled) {
    background-color: #4b5563;
    transform: scale(1.03);
    box-shadow: 0 4px 16px 0 rgba(55,65,81,0.12);
  }
  &:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
    opacity: 0.7;
  }
}

.form-select, .form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #fff;
  color: #374151;
  transition: border-color 0.2s, box-shadow 0.2s;

  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px #bfdbfe;
    outline: none;
  }
  &:hover:not(:disabled) {
    border-color: #60a5fa;
  }
}

.bg-gray-50.rounded-md.p-4.mb-6 {
  box-shadow: 0 1px 4px 0 rgba(55,65,81,0.04);
  border: 1px solid #e5e7eb;
}

.bg-yellow-50 {
  border-left: 4px solid #fde68a;
  padding-left: 1rem;
}