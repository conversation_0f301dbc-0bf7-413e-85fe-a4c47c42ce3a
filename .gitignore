HELP.md
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### Node.js ###
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
.npm
.yarn-integrity

### Angular ###
.angular/
dist/
/dist
/tmp
/out-tsc
/bazel-out

### Java/Maven ###
.jdks/
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar
hs_err_pid*
replay_pid*

### Logs ###
*.log
logs/
*.log.*

### OS ###
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

### Editor ###
*.swp
*.swo
*~
.#*
\#*#

### Environment ###
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

### Database ###
*.db
*.sqlite
*.sqlite3

### Temporary files ###
*.tmp
*.temp
*.cache
.cache/

### Application specific ###
uploads/
data/
sample-data/