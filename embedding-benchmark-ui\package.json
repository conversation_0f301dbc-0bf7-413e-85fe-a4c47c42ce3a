{"name": "embedding-benchmark-ui", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development"}, "private": true, "dependencies": {"@angular/animations": "^19.2.13", "@angular/common": "^19.2.13", "@angular/compiler": "^19.2.13", "@angular/core": "^19.2.13", "@angular/forms": "^19.2.13", "@angular/platform-browser": "^19.2.13", "@angular/platform-browser-dynamic": "^19.2.13", "@angular/router": "^19.2.13", "chart.js": "^4.4.0", "ng2-charts": "^4.0.0", "rxjs": "~7.8.1", "zone.js": "^0.15.0", "tslib": "^2.6.2"}, "devDependencies": {"@angular/cli": "^19.2.13", "@angular-devkit/build-angular": "^19.2.13", "@angular/compiler-cli": "^19.2.13", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.2.0"}}