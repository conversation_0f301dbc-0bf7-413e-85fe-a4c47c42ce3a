package os.tukan.extractor.service;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import os.tukan.extractor.config.GroundTruthDataset;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

@Service
@Log4j2
public class MetricsService {

    /**
     * Relevance score (from ground-truth) that counts as “fully relevant”
     * for binary metrics (Precision, Recall, F1, MRR).
     */
    public static final int FULL_RELEVANCE_THRESHOLD = 4;
    private final AtomicLong totalStorageTime = new AtomicLong(0L);

    private final AtomicLong totalEmbeddingTime = new AtomicLong(0L);
    private final AtomicLong totalQueryTime = new AtomicLong(0L);
    private final AtomicLong totalIndexTime = new AtomicLong(0L);
    private final AtomicLong storageCount = new AtomicLong(0L);

    private final AtomicLong embeddingCount = new AtomicLong(0L);
    private final AtomicLong queryCount = new AtomicLong(0L);
    private final AtomicLong indexCount = new AtomicLong(0L);

    public synchronized void addDocumentMetrics(String docName, long charCount, long embedTimeMs, long storageTimeMs, long indexTimeMs) {
        documentDetails.add(new DocumentMetrics(docName, charCount, embedTimeMs, storageTimeMs, indexTimeMs));

        totalEmbeddedChars.addAndGet(charCount);
        totalEmbeddingTime.addAndGet(embedTimeMs);
        totalStorageTime.addAndGet(storageTimeMs);
        totalIndexTime.addAndGet(indexTimeMs);

        embeddingCount.incrementAndGet();
        storageCount.incrementAndGet();
        indexCount.incrementAndGet();
    }

    private final AtomicLong totalEmbeddedChars = new AtomicLong(0L);

    @Getter
    private final List<DocumentMetrics> documentDetails = new ArrayList<>();

    public double getAvgEmbeddingTimePerDoc() {
        long count = embeddingCount.get();
        return count == 0 ? 0.0 : (double) totalEmbeddingTime.get() / count;
    }

    public void addQueryTime(long timeMs) {
        totalQueryTime.addAndGet(timeMs);
        queryCount.incrementAndGet();
    }

    public double getAvgStorageTimePerDoc() {
        long count = storageCount.get();
        return count == 0 ? 0.0 : (double) totalStorageTime.get() / count;
    }

    public double getAvgQueryTime() {
        long count = queryCount.get();
        return count == 0 ? 0.0 : (double) totalQueryTime.get() / count;
    }

    public double getAvgEmbeddingTimePerThousandChars() {
        long chars = totalEmbeddedChars.get();
        return chars == 0 ? 0.0 : (double) totalEmbeddingTime.get() / (chars / 1000.0);
    }

    public double getAvgStorageTimePerThousandChars() {
        long chars = totalEmbeddedChars.get();
        return chars == 0 ? 0.0 : (double) totalStorageTime.get() / (chars / 1000.0);
    }

    public double getAvgIndexingTimePerThousandChars() {
        long chars = totalEmbeddedChars.get();
        return chars == 0 ? 0.0 : (double) totalIndexTime.get() / (chars / 1000.0);
    }

    public double getAvgIndexingTimePerDoc() {
        long count = indexCount.get();
        return count == 0 ? 0.0 : (double) totalIndexTime.get() / count;
    }

    /**
     * Calculates IR metrics (Precision, Recall, F1, nDCG, MRR) for a query
     * and logs the result.  Ranking order in {@code retrievedDocIds} is
     * preserved, so pass a List – not a Set.
     *
     * @param provider        e.g. "elasticsearch", "chroma", …
     * @param query           natural-language query string
     * @param retrievedDocIds ranked list of document IDs returned by search
     * @return the computed metrics
     */
    public IrMetricsResult computeAndAddIrMetrics(String provider,
                                                  String query,
                                                  List<String> retrievedDocIds) {

        Map<String, Integer> relevanceMap = GroundTruthDataset.getGradedRelevance(query);

        if (relevanceMap.isEmpty()) {
            log.warn("No ground-truth relevance found for query: '{}'", query);
            return new IrMetricsResult(0, 0, 0, 0, 0);
        }
        if (retrievedDocIds == null || retrievedDocIds.isEmpty()) {
            log.warn("No documents retrieved for query: '{}'", query);
            return new IrMetricsResult(0, 0, 0, 0, 0);
        }

        int relevantRetrieved = 0;
        double dcg = 0;
        double mrr = 0;
        int rank = 0;
        boolean firstRelevantHit = false;
        Set<String> seen = new HashSet<>();   // skip duplicates

        for (String rawId : retrievedDocIds) {

            String docId = GroundTruthDataset.canonicalDocId(rawId);
            if (!seen.add(docId)) {          // duplicate in result list
                rank++;
                continue;
            }

            int rel = relevanceMap.getOrDefault(docId, 0);

            boolean isFullyRelevant = rel >= FULL_RELEVANCE_THRESHOLD;
            if (isFullyRelevant) {
                relevantRetrieved++;
                if (!firstRelevantHit) {
                    mrr = 1d / (rank + 1d);     // rank is 0-based
                    firstRelevantHit = true;
                }
            }

            if (rel > 0) {
                dcg += (Math.pow(2, rel) - 1) /
                        (Math.log(rank + 2) / Math.log(2));
            }
            rank++;
        }

        long totalFullyRelevant = relevanceMap.values().stream()
                .filter(r -> r >= FULL_RELEVANCE_THRESHOLD)
                .count();

        double precision = relevantRetrieved / (double) rank;
        double recall = totalFullyRelevant == 0 ? 0
                : relevantRetrieved / (double) totalFullyRelevant;
        double f1 = (precision + recall == 0) ? 0
                : 2 * precision * recall / (precision + recall);


        List<Integer> ideal = relevanceMap.values().stream()
                .filter(v -> v > 0)
                .sorted(Comparator.reverseOrder())
                .limit(rank)
                .toList();

        double idealDcg = 0;
        for (int i = 0; i < ideal.size(); i++) {
            idealDcg += (Math.pow(2, ideal.get(i)) - 1) /
                    (Math.log(i + 2) / Math.log(2));
        }
        double ndcg = idealDcg == 0 ? 0 : dcg / idealDcg;

        IrMetricsResult result = new IrMetricsResult(precision, recall, ndcg, f1, mrr);

        log.info("IR Metrics [{}] – \"{}\"  P:{}, R:{}, nDCG:{}, F1:{}, MRR:{}",
                provider, query, precision, recall, ndcg, f1, mrr);

        return result;
    }

    public long getTotalQueryCount() {
        return queryCount.get();
    }

    public long getTotalEmbeddedChars() {
        return totalEmbeddedChars.get();
    }

    public static class DocumentMetrics {
        public final String docName;
        public final long charCount;
        public final long embedTimeMs;
        public final long storageTimeMs;
        public final long indexTimeMs;

        public DocumentMetrics(String docName, long charCount, long embedTimeMs, long storageTimeMs, long indexTimeMs) {
            this.docName = docName;
            this.charCount = charCount;
            this.embedTimeMs = embedTimeMs;
            this.storageTimeMs = storageTimeMs;
            this.indexTimeMs = indexTimeMs;
        }
    }


    public record IrMetricsResult(
            double precision,
            double recall,
            double ndcg,
            double f1,
            double mrr) {
    }
}