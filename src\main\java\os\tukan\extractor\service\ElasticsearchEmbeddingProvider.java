package os.tukan.extractor.service;

import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import os.tukan.extractor.config.EmbeddingModelConfig;
import os.tukan.extractor.vectorDB.ElasticService;

@Log4j2
@Component
public class ElasticsearchEmbeddingProvider implements EmbeddingProvider {
    
    private final ElasticService elasticService;
    @Setter
    private EmbeddingModelConfig config;
    
    public ElasticsearchEmbeddingProvider(ElasticService elasticService) {
        this.elasticService = elasticService;
    }

    @Override
    public float[] embed(String text) throws Exception {
        if (config == null) {
            throw new IllegalStateException("Embedding model config not set");
        }
        
        try {
            return elasticService.getEmbeddingFromElasticsearch(text);
        } catch (Exception e) {
            log.error("Failed to generate embedding using Elasticsearch: {}", e.getMessage(), e);
            throw new Exception("Elasticsearch embedding generation failed: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean isAvailable() {
        try {
            // Test with a simple text
            float[] testEmbedding = elasticService.getEmbeddingFromElasticsearch("test");
            return testEmbedding != null && testEmbedding.length > 0;
        } catch (Exception e) {
            log.warn("Elasticsearch embedding provider not available: {}", e.getMessage());
            return false;
        }
    }

}
