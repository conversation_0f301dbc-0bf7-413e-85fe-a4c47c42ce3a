package os.tukan.extractor.exception;

/**
 * Exception thrown when configuration validation fails.
 * Provides specific error context for configuration-related issues.
 */
public class ConfigurationException extends Exception {

    private final String configField;
    private final Object configValue;

    public ConfigurationException(String message) {
        super(message);
        this.configField = null;
        this.configValue = null;
    }

    public ConfigurationException(String message, String configField, Object configValue) {
        super(String.format("Configuration error in field '%s' with value '%s': %s", configField, configValue, message));
        this.configField = configField;
        this.configValue = configValue;
    }

    public ConfigurationException(String message, Throwable cause) {
        super(message, cause);
        this.configField = null;
        this.configValue = null;
    }

    public String getConfigField() {
        return configField;
    }

    public Object getConfigValue() {
        return configValue;
    }
}
