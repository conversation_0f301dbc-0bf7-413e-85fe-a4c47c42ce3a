package os.tukan.extractor.service.warmup;

import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Component;
import os.tukan.extractor.exception.EmbeddingProviderException;
import os.tukan.extractor.service.EmbeddingProvider;

/**
 * Simple warmup strategy that provides basic warmup functionality
 * without requiring complex configuration. Used as a fallback when
 * ComprehensiveWarmupStrategy is not available.
 */
@Log4j2
@Component
@ConditionalOnMissingBean(name = "comprehensiveWarmupStrategy")
public class SimpleWarmupStrategy implements WarmupStrategy {

    private static final int DEFAULT_WARMUP_ITERATIONS = 3;
    private static final int MAX_TEXT_LENGTH = 10000;

    @Override
    public void performWarmup(final EmbeddingProvider provider, final String actualText) throws EmbeddingProviderException {
        if (provider == null) {
            throw new EmbeddingProviderException("Provider cannot be null for warmup");
        }
        if (actualText == null) {
            throw new EmbeddingProviderException("Actual text cannot be null for warmup");
        }

        final String providerId = provider.getClass().getSimpleName();
        
        try {
            log.debug("Starting simple warmup for provider: {}", providerId);
            final long warmupStartTime = System.nanoTime();

            // Basic warmup with simple text
            for (int i = 0; i < DEFAULT_WARMUP_ITERATIONS; i++) {
                provider.embed("warmup text " + i);
            }

            final long warmupDuration = (System.nanoTime() - warmupStartTime) / 1_000_000;
            log.debug("Simple warmup completed in {}ms for provider: {}", warmupDuration, providerId);

        } catch (final Exception e) {
            throw new EmbeddingProviderException("Simple warmup failed", providerId, "simple_warmup", e);
        }
    }

    @Override
    public String getStrategyName() {
        return "simple";
    }

    @Override
    public boolean isApplicableForTextLength(final int textLength) {
        return textLength <= MAX_TEXT_LENGTH;
    }
}
