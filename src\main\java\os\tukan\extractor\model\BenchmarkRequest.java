package os.tukan.extractor.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BenchmarkRequest {

    private String modelId;
    private String datasetId;
    private List<String> queries;
    private Map<String, List<String>> groundTruth;
    private Map<String, Map<String, Integer>> gradedGroundTruth;
    private String similarityFunction = "cosine";
    private int topK = 5;
    private String jobId;

}
