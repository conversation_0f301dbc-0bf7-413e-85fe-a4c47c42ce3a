# Warmup configuration for EmbeddingModelManager
embedding:
  warmup:
    # Number of basic warmup iterations to eliminate cold start effects
    basic-warmup-iterations: 3
    
    # Character threshold for determining warmup text strategy
    character-threshold: 50
    
    # Number of timing samples to take for statistical accuracy
    timing-samples: 3
    
    # Base text pattern for warmup operations
    base-warmup-text: "warmup text"
    
    # Progressive warmup texts for different length scenarios
    progressive-warmup-texts:
      - "short"
      - "medium length warmup text for testing"
      - "longer warmup text that simulates more realistic document content for comprehensive testing"
    
    # Whether to enable all warmup phases (can be disabled for performance)
    enable-progressive-warmup: true
    enable-similar-length-warmup: true
    enable-exact-length-warmup: true
    
    # Maximum text length for warmup operations (prevents memory issues)
    max-warmup-text-length: 10000
    
    # Timeout for individual warmup operations
    warmup-timeout: PT30S
    
    # Whether to log detailed warmup phase information
    enable-detailed-logging: false
