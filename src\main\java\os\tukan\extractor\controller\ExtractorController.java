package os.tukan.extractor.controller;

import lombok.extern.log4j.Log4j2;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import os.tukan.extractor.config.ExtractorProperties;
import os.tukan.extractor.service.ExtractorService;
import os.tukan.extractor.service.MetricsService;
import os.tukan.extractor.vectorDB.ChromaService;
import os.tukan.extractor.vectorDB.ElasticService;
import os.tukan.extractor.vectorDB.OpenSearchVectorService;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.*;

@Log4j2
@RestController
@RequestMapping("/pdf")

public class ExtractorController {

    private final ExtractorService extractorService;
    private final ExtractorProperties extractorProperties;
    private final ElasticService elasticService;
    private final ChromaService chromaService;
    private final OpenSearchVectorService openSearchVectorService;

    private final MetricsService metricsService;


    public ExtractorController(ExtractorService extractorService,
                               ExtractorProperties extractorProperties,
                               ElasticService elasticService, ChromaService chromaService, OpenSearchVectorService openSearchVectorService, MetricsService metricsService) {
        this.extractorService = extractorService;
        this.extractorProperties = extractorProperties;
        this.elasticService = elasticService;
        this.chromaService = chromaService;
        this.openSearchVectorService = openSearchVectorService;
        this.metricsService = metricsService;
    }

    @PostMapping("/updateConfig")
    public ResponseEntity<?> updateConfig(
            @RequestParam(value = "targetUrl", required = false) String targetUrl,
            @RequestParam(value = "path", required = false) String path) {

        if (targetUrl != null && !targetUrl.isBlank()) {
            extractorProperties.setTargetUrl(targetUrl);
        }
        if (path != null && !path.isBlank()) {
            extractorProperties.setPath(path);
        }
        log.info("Updated config: targetUrl={}, path={}",
                extractorProperties.getTargetUrl(), extractorProperties.getPath());

        return ResponseEntity.ok(Map.of(
                "message", "Config updated successfully",
                "targetUrl", extractorProperties.getTargetUrl(),
                "path", extractorProperties.getPath()
        ));
    }

    @PostMapping(value = "/chromaPdf", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> chromaPdf(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body("No file was provided");
            }
            // Extract text in chunks (one per page)
            String chunks = extractorService.extractTextFromPdf(file);
            String docId = UUID.randomUUID().toString();
            // Prepare a base metadata map (file-specific info)
            Map<String, Object> metadata = Map.of("file_name", Objects.requireNonNull(file.getOriginalFilename()));
            chromaService.storeEmbedding(List.of(chunks), metadata, docId);
            return ResponseEntity.ok(Map.of("message", "PDF extracted, embedded and stored successfully"));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body("Error processing PDF file: " + e.getMessage());
        }
    }

    @PostMapping(value = "/elasticPdf", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> elasticPdf(@RequestParam("file") MultipartFile file) {
        try {
            elasticService.initializeElasticsearchResources();
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body("No file was provided");
            }
            // Extract text chunks from the file
            List<String> chunks = extractorService.extractTextFromPdfWithChunk(file);
            String docId = UUID.randomUUID().toString();
            // Index each chunk separately with page-specific metadata
            for (int i = 0; i < chunks.size(); i++) {
                Map<String, Object> metadata = Map.of("file_name", Objects.requireNonNull(file.getOriginalFilename()), "page_number", i + 1);
                float[] embedding = elasticService.generateEmbedding(chunks.get(i));
                elasticService.indexDocument(docId, chunks.get(i), embedding);
            }
            return ResponseEntity.ok(Map.of("message", "Indexed PDF with docId=" + docId + " extracted, embedded and stored successfully"));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body("Error processing PDF file: " + e.getMessage());
        }
    }

    @PostMapping(value = "/openSearch", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> openSearchPdf(@RequestParam("file") MultipartFile file) {
        try {
            elasticService.initializeElasticsearchResources();
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body("No file was provided");
            }
            List<String> chunks = extractorService.extractTextFromPdfWithChunk(file);
            String docId = UUID.randomUUID().toString();
            for (int i = 0; i < chunks.size(); i++) {
                openSearchVectorService.indexDocument(docId, chunks.get(i), Map.of("file_name", file.getName()));
            }
            return ResponseEntity.ok(Map.of("message", "Indexed PDF with docId=" + docId + " extracted, embedded and stored successfully"));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body("Error processing PDF file: " + e.getMessage());
        }
    }

    @PostMapping("/chromaDirectory")
    public ResponseEntity<?> chromaDirectory() {
        String path = extractorProperties.getPath();
        File directory = new File(path);
        if (!directory.exists() || !directory.isDirectory()) {
            return ResponseEntity.badRequest().body("Invalid directory path");
        }
        File[] files = directory.listFiles();
        if (files == null) {
            return ResponseEntity.badRequest().body("Unable to list files");
        }
        long overallStart = System.currentTimeMillis();
        int count = 0;
        try {
            for (File f : files) {
                if (f.isFile() && f.getName().toLowerCase().endsWith(".pdf")) {
                    String text = extractorService.extractTextFromFile(f);
                    log.info("Extracted text length: {} from file: {}", text.length(), f.getName());
                    String docId = f.getName() + "_" + System.currentTimeMillis();
                    chromaService.storeEmbedding(List.of(text), Map.of("file_name", f.getName()), docId);
                    count++;
                }
            }
            long totalTime = System.currentTimeMillis() - overallStart;
            log.info("Processed {} PDFs in {} ms ({} ms/PDF).", count, totalTime, (count == 0 ? 0 : totalTime / count));
            return ResponseEntity.ok(Map.of(
                    "message", "All PDFs extracted, embedded, and stored successfully.",
                    "totalTimeMs", totalTime,
                    "docsProcessed", count
            ));
        } catch (Exception e) {
            log.error("Error processing PDFs", e);
            return ResponseEntity.internalServerError()
                    .body("Error processing PDF files: " + e.getMessage());
        }
    }

    @PostMapping("/elasticDirectory")
    public ResponseEntity<?> elasticDirectory() {
        String path = extractorProperties.getPath();
        File directory = new File(path);
        if (!directory.exists() || !directory.isDirectory()) {
            return ResponseEntity.badRequest().body("Invalid directory path");
        }
        File[] pdfFiles = directory.listFiles((dir, name) -> name.toLowerCase().endsWith(".pdf"));
        if (pdfFiles == null || pdfFiles.length == 0) {
            return ResponseEntity.badRequest().body("No PDF files found in the directory");
        }
        long overallStart = System.currentTimeMillis();
        int count = 0;
        for (File pdf : pdfFiles) {
            try {
                String text = extractorService.extractTextFromFile(pdf);
                log.info("Extracted text length from {}: {}", pdf.getName(), text.length());
                String docId = pdf.getName() + "_" + System.currentTimeMillis();

                elasticService.indexDocumentWithEmbedding(docId, text);
                count++;
            } catch (Exception e) {
                log.error("Error processing file {}: {}", pdf.getName(), e.getMessage(), e);
            }
        }
        long overallEnd = System.currentTimeMillis();
        long totalTime = overallEnd - overallStart;
        log.info("Processed {} PDF files in {} ms", count, totalTime);
        return ResponseEntity.ok(Map.of(
                "message", "All PDFs in directory uploaded to Elastic successfully",
                "docsProcessed", count,
                "totalTimeMs", totalTime
        ));
    }

    private static String getBasicAuthenticationHeader() {
        String valueToEncode = "Administrator" + ":" + "optimal";
        return "Basic " + Base64.getEncoder().encodeToString(valueToEncode.getBytes());
    }

    private void sendText(String text) throws IOException, InterruptedException {
        HttpClient client = HttpClient.newHttpClient();
        String url = extractorProperties.getTargetUrl();

        HttpRequest request = HttpRequest.newBuilder()
                .header("Authorization", getBasicAuthenticationHeader())
                .uri(URI.create(url))
                .POST(HttpRequest.BodyPublishers.ofString(text))
                .build();
        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            throw new IOException(response.body());
        }
    }

}
