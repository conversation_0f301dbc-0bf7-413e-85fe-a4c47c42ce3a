package os.tukan.extractor.config;


import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Setter
@Getter
@RequiredArgsConstructor
@Component
@ConfigurationProperties(prefix = "extractor")
public class ExtractorProperties {
    String path;
    String targetUrl;
}
