package os.tukan.extractor.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ComparisonResult {
    
    private BenchmarkResult primary;
    private BenchmarkResult secondary;
    private ComparisonMetrics overallComparison;
    private List<QueryComparisonResult> queryComparisons;
}
