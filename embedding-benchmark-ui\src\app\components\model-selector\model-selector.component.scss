/* Model selector specific styles */

.form-select {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  font-size: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #fff;
  color: #374151;

  /* Custom arrow SVG */
  background-image: url("data:image/svg+xml,%3Csvg fill='none' stroke='gray' stroke-width='2' viewBox='0 0 24 24'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M19 9l-7 7-7-7'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1.25em 1.25em;
  transition: border-color 0.2s, box-shadow 0.2s;

  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px #bfdbfe;
    outline: none;
  }
  &:hover:not(:disabled) {
    border-color: #60a5fa;
  }
  &:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
  }
}

.btn-secondary {
  display: inline-flex;
  align-items: center;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 0.375rem;
  background-color: #fff;
  border-width: 1px;
  border-color: #d1d5db;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  transition: background-color 0.2s, color 0.2s;
}

.loading-spinner {
  width: 1.25rem;
  height: 1.25rem;
  border-width: 2px;
  border-style: solid;
  border-color: #d1d5db;
}

.simple-spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 3px solid #d1d5db;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: simple-spin 0.8s linear infinite;
  display: inline-block;
}

.card {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  border-radius: 0.5rem;
  padding: 1.5rem;
  border-width: 1px;
  border-color: #e5e7eb;

  .model-info {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 0.375rem;
    background-color: #eff6ff;
    border-width: 1px;
    border-color: #bfdbfe;
    font-size: 0.875rem;
    color: #1e40af;
  }
}

.fa-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes simple-spin {
  to { transform: rotate(360deg); }
}
