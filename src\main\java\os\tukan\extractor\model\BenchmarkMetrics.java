package os.tukan.extractor.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BenchmarkMetrics {

    private double avgPrecision;
    private double avgRecall;
    private double avgF1;
    private double avgNdcg;
    private double avgMrr;
    private double avgQueryTimeMs;
    private int totalQueries;
    private int totalDocuments;

    // Embedding time metrics
    private EmbeddingTimeMetrics embeddingTimeMetrics;

    public BenchmarkMetrics(double avgPrecision, double avgRecall, double avgF1, double avgNdcg,
                            double avgMrr, double avgQueryTimeMs, int totalQueries, int totalDocuments) {
        this.avgPrecision = avgPrecision;
        this.avgRecall = avgRecall;
        this.avgF1 = avgF1;
        this.avgNdcg = avgNdcg;
        this.avgMrr = avgMrr;
        this.avgQueryTimeMs = avgQueryTimeMs;
        this.totalQueries = totalQueries;
        this.totalDocuments = totalDocuments;
        this.embeddingTimeMetrics = EmbeddingTimeMetrics.empty();
    }

    public static BenchmarkMetrics empty() {
        return new BenchmarkMetrics(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0);
    }
}
