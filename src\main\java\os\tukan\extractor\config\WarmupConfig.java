package os.tukan.extractor.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * Configuration properties for embedding model warmup strategy.
 * Externalizes warmup parameters for better maintainability and performance tuning.
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "embedding.warmup")
public class WarmupConfig {

    /**
     * Number of basic warmup iterations to eliminate cold start effects
     */
    private int basicWarmupIterations = 3;

    /**
     * Character threshold for determining warmup text strategy
     */
    private int characterThreshold = 50;

    /**
     * Number of timing samples to take for statistical accuracy
     */
    private int timingSamples = 3;

    /**
     * Base text pattern for warmup operations
     */
    private String baseWarmupText = "warmup text";

    /**
     * Progressive warmup texts for different length scenarios
     */
    private String[] progressiveWarmupTexts = {
        "short",
        "medium length warmup text for testing",
        "longer warmup text that simulates more realistic document content for comprehensive testing"
    };

    /**
     * Whether to enable all warmup phases (can be disabled for performance)
     */
    private boolean enableProgressiveWarmup = true;
    private boolean enableSimilarLengthWarmup = true;
    private boolean enableExactLengthWarmup = true;

    /**
     * Maximum text length for warmup operations (prevents memory issues)
     */
    private int maxWarmupTextLength = 10000;

    /**
     * Timeout for individual warmup operations
     */
    private Duration warmupTimeout = Duration.ofSeconds(30);

    /**
     * Whether to log detailed warmup phase information
     */
    private boolean enableDetailedLogging = false;
}
