package os.tukan.extractor.controller;


import io.micrometer.common.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import os.tukan.extractor.config.GroundTruthDataset;
import os.tukan.extractor.service.ExtractorService;
import os.tukan.extractor.service.MetricsService;
import os.tukan.extractor.vectorDB.ChromaService;
import os.tukan.extractor.vectorDB.ElasticService;
import os.tukan.extractor.vectorDB.OpenSearchVectorService;
import os.tukan.extractor.vectorDB.PipelineElasticSearch;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@RestController
@RequestMapping("/")
public class TestController {

    private final ExtractorService extractorService;
    private final ChromaService embeddingService;
    private final ElasticService elasticService;
    private final ChromaService chromaService;
    private final PipelineElasticSearch pipelineElastic;
    private final OpenSearchVectorService openSearchVectorService;

    private final MetricsService metricsService;

    public TestController(ExtractorService extractorService, ChromaService embeddingService, ElasticService elasticService, ChromaService chromaService, PipelineElasticSearch pipelineElastic, OpenSearchVectorService openSearchVectorService, MetricsService metricsService) {
        this.extractorService = extractorService;
        this.embeddingService = embeddingService;
        this.elasticService = elasticService;
        this.chromaService = chromaService;
        this.pipelineElastic = pipelineElastic;
        this.openSearchVectorService = openSearchVectorService;
        this.metricsService = metricsService;
    }


    @PostMapping("/uploadAll")
    @Operation(summary = "Bulk upload PDFs to Chroma", description = "Reads PDFs from a configured directory, extracts text, and stores them in Chroma.")
    public ResponseEntity<?> uploadAll() {
        String path = "";
        File directory = new File(path);
        if (!directory.exists() || !directory.isDirectory()) {
            return ResponseEntity.badRequest().body("Invalid directory path");
        }
        File[] files = directory.listFiles();
        if (files == null) {
            return ResponseEntity.badRequest().body("Unable to list files");
        }
        long overallStart = System.currentTimeMillis();
        int count = 0;
        try {
            for (File f : files) {
                if (f.isFile() && f.getName().toLowerCase().endsWith(".pdf")) {
                    // Extract text
                    String text = extractorService.extractTextFromFile(f);
                    log.info("Extracted text length: {} from file: {}", text.length(), f.getName());
                    String docId = f.getName().substring(0, f.getName().toLowerCase().indexOf(".pdf"));
                    embeddingService.storeEmbedding(List.of(text), Map.of("file_name", f.getName()), docId);
                    count++;
                }
            }
            long totalTime = System.currentTimeMillis() - overallStart;
            log.info("Processed {} PDFs in {} ms ({} ms/PDF).", count, totalTime, (count == 0 ? 0 : totalTime / count));
            return ResponseEntity.ok(Map.of(
                    "message", "All PDFs extracted, embedded, and stored in Chroma successfully.",
                    "totalTimeMs", totalTime,
                    "docsProcessed", count));
        } catch (Exception e) {
            log.error("Error processing PDFs", e);
            return ResponseEntity.internalServerError()
                    .body("Error processing PDF files: " + e.getMessage());
        }
    }

    @PostMapping("/uploadDirectoryInElastic")
    @Operation(summary = "Bulk upload PDFs to Elasticsearch", description = "Reads PDFs from a configured directory, extracts text, and indexes them in Elasticsearch.")
    public ResponseEntity<?> uploadDirectoryInElastic() {
        String path = "C:\\Users\\<USER>\\Desktop\\Microsoft Teams-Chatdateien";
        File directory = new File(path);
        if (!directory.exists() || !directory.isDirectory()) {
            return ResponseEntity.badRequest().body("Invalid directory path");
        }
        File[] pdfFiles = directory.listFiles((dir, name) -> name.toLowerCase().endsWith(".pdf"));
        if (pdfFiles == null || pdfFiles.length == 0) {
            return ResponseEntity.badRequest().body("No PDF files found in the directory");
        }
        long overallStart = System.currentTimeMillis();
        int count = 0;
        for (File pdf : pdfFiles) {
            try {
                String text = extractorService.extractTextFromFile(pdf);
                log.info("Extracted text length from {}: {}", pdf.getName(), text.length());

                String docId = pdf.getName().substring(0, pdf.getName().toLowerCase().indexOf(".pdf"));


                elasticService.indexDocumentWithEmbedding(docId, text);
                count++;

            } catch (Exception e) {
                log.error("Error processing file {}: {}", pdf.getName(), e.getMessage(), e);
            }
        }

        long overallEnd = System.currentTimeMillis();
        long totalTime = overallEnd - overallStart;
        log.info("Processed {} PDF files in {} ms", count, totalTime);
        return ResponseEntity.ok(Map.of(
                "message", "All PDFs in directory uploaded to Elastic successfully",
                "docsProcessed", count,
                "totalTimeMs", totalTime));
    }





    @GetMapping("/searchElastic")
    public ResponseEntity<?> searchElastic(@RequestParam("query") String queryText,
                                           @RequestParam(value = "topK", defaultValue = "5") int topK) {

        long startTime = System.currentTimeMillis();

        try {
            if (StringUtils.isBlank(queryText)) {
                return ResponseEntity.badRequest().body("Query text cannot be null or empty");
            }

            float[] queryVector = elasticService.getEmbeddingFromElasticsearch(queryText);
            log.info("Embedding vector length: {}", queryVector);
            List<Map<String, Object>> results = elasticService.knnSearch(queryVector, topK);

            long durationMs = System.currentTimeMillis() - startTime;
            metricsService.addQueryTime(durationMs);
            log.info("Embedding + KNN search time: {} ms", durationMs);

            List<String> retrievedDocIds = (results == null) ? List.of()
                    : results.stream()
                    .filter(r -> r.containsKey("id"))
                    .map(r -> r.get("id").toString())
                    .collect(Collectors.toCollection(ArrayList::new));


            List<String> groundTruth = GroundTruthDataset.getRelevantDocsSorted(queryText).stream()
                    .map(Map.Entry::getKey)
                    .toList();

            MetricsService.IrMetricsResult irResult =
                    metricsService.computeAndAddIrMetrics("elasticsearch", queryText, retrievedDocIds);

            return ResponseEntity.ok(Map.of(
                    "query", queryText,
                    "topK", topK,
                    "durationMs", durationMs,
                    "retrievedDocIds", retrievedDocIds,
                    "groundTruthDocIds", groundTruth,
                    "irMetrics", irResult,
                    "results", results != null ? results : List.of()
            ));
        } catch (Exception e) {
            log.error("Error in /searchElastic", e);
            return ResponseEntity.internalServerError()
                    .body("Error processing search request: " + e.getMessage());
        }
    }

    /* =====================================================================
     * /searchChroma
     * ===================================================================== */
    @GetMapping("/searchChroma")
    public ResponseEntity<?> searchChroma(@RequestParam("query") String queryText,
                                          @RequestParam(value = "topK", defaultValue = "5") int topK) {

        long startTime = System.currentTimeMillis();

        try {
            if (StringUtils.isBlank(queryText)) {
                return ResponseEntity.badRequest().body("Query text cannot be null or empty");
            }

            var response = chromaService.query(queryText, topK);

            long queryTime = System.currentTimeMillis() - startTime;
            metricsService.addQueryTime(queryTime);

            List<String> retrievedDocIds = new ArrayList<>();
            if (response.getDocuments() != null) {
                for (Object obj : response.getDocuments()) {
                    if (obj instanceof Map<?, ?> docMap && docMap.containsKey("docId")) {
                        retrievedDocIds.add(docMap.get("docId").toString());
                    }
                }
            }

            MetricsService.IrMetricsResult irResult =
                    metricsService.computeAndAddIrMetrics("chroma", queryText, retrievedDocIds);

            return ResponseEntity.ok(Map.of(
                    "message", "Chroma search completed",
                    "resultCount", retrievedDocIds.size(),
                    "irMetrics", irResult,
                    "queryTimeMs", queryTime
            ));
        } catch (Exception e) {
            log.error("Error in /searchChroma", e);
            return ResponseEntity.internalServerError()
                    .body("Error processing search request: " + e.getMessage());
        }
    }

    /* =====================================================================
     * /searchPipelineIngest
     * ===================================================================== */
    @GetMapping("/searchPipelineIngest")
    public ResponseEntity<?> searchPipelineIngest(@RequestParam("query") String queryText,
                                                  @RequestParam(value = "topK", defaultValue = "5") int topK) {

        long startTime = System.currentTimeMillis();

        try {
            if (StringUtils.isBlank(queryText)) {
                return ResponseEntity.badRequest().body("Query text cannot be null or empty");
            }

            List<Map<String, Object>> response = pipelineElastic.semanticSearch(queryText, topK);

            long queryTime = System.currentTimeMillis() - startTime;
            metricsService.addQueryTime(queryTime);

            List<String> retrievedDocIds = new ArrayList<>();
            if (response != null) {
                log.info(response.toString());
                for (Object obj : response) {
                    if (obj instanceof Map<?, ?> docMap && docMap.containsKey("id")) {
                        retrievedDocIds.add(docMap.get("id").toString());
                    }
                }
            }

            List<String> groundTruth = GroundTruthDataset.getRelevantDocsSorted(queryText).stream()
                    .map(Map.Entry::getKey)
                    .toList();


            MetricsService.IrMetricsResult irResult =
                    metricsService.computeAndAddIrMetrics("pipeline", queryText, retrievedDocIds);

            return ResponseEntity.ok(Map.of(
                    "message", "Pipeline search completed",
                    "resultCount", retrievedDocIds,
                    "groundTruthDocIds", groundTruth,
                    "irMetrics", irResult,
                    "response", response,
                    "queryTimeMs", queryTime
            ));
        } catch (Exception e) {
            log.error("Error in /searchPipelineIngest", e);
            return ResponseEntity.internalServerError()
                    .body("Error processing search request: " + e.getMessage());
        }
    }


    @GetMapping("/searchOpenSearch")
    public ResponseEntity<?> searchOpenSearch(@RequestParam("query") String queryText,
                                              @RequestParam(value = "topK", defaultValue = "10") int topK) {
        long startTime = System.currentTimeMillis();
        try {
            if (queryText == null || queryText.isBlank()) {
                return ResponseEntity.badRequest().body("Query text cannot be null or empty");
            }
            List<Map<String, Object>> results = openSearchVectorService.knnSearch(queryText, topK);
            long queryTime = System.currentTimeMillis() - startTime;
            metricsService.addQueryTime(queryTime);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body("Error processing search request: " + e.getMessage());
        }
    }
}


